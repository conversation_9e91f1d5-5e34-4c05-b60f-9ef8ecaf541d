import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dasso_reader/utils/performance/performance_metrics.dart';
import 'package:dasso_reader/utils/performance/device_testing_framework.dart';
import 'package:dasso_reader/utils/performance/performance_improvement_validator.dart';
import 'package:dasso_reader/utils/performance/performance_monitoring_dashboard.dart';
import 'package:dasso_reader/utils/performance/orientation_change_monitor.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';

/// Comprehensive integration test for tablet performance optimization
/// Validates all systems are working correctly and production-ready
void main() {
  group('🚀 Tablet Performance Integration Tests', () {
    late PerformanceMetrics performanceMetrics;
    late DeviceTestingFramework deviceTesting;
    late PerformanceImprovementValidator validator;
    late PerformanceMonitoringDashboard dashboard;
    late OrientationChangeMonitor orientationMonitor;

    setUpAll(() async {
      // Initialize all performance systems
      performanceMetrics = PerformanceMetrics();
      deviceTesting = DeviceTestingFramework();
      validator = PerformanceImprovementValidator();
      dashboard = PerformanceMonitoringDashboard();
      orientationMonitor = OrientationChangeMonitor();

      await performanceMetrics.initialize();
      await deviceTesting.initialize();
      await validator.initialize();
      await dashboard.initialize();
    });

    testWidgets('✅ Performance Metrics System Integration', (WidgetTester tester) async {
      // Create test app
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: Container(child: Text('Test App')),
        ),
      ));

      // Test tablet device simulation
      await tester.binding.setSurfaceSize(const Size(1024, 768)); // Tablet landscape
      await tester.pumpAndSettle();

      final context = tester.element(find.byType(MaterialApp));

      // Test 1: PerformanceMetrics with tablet context
      performanceMetrics.startMonitoring();
      await tester.pump(const Duration(seconds: 1));
      
      final report = performanceMetrics.getPerformanceReport(context: context);
      expect(report, isNotNull);
      expect(report.tabletMetrics, isNotNull, reason: 'Tablet metrics should be available on tablet devices');
      
      performanceMetrics.stopMonitoring();
      
      print('✅ PerformanceMetrics integration: PASSED');
    });

    testWidgets('✅ Device Testing Framework Integration', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(body: Container(child: Text('Test App'))),
      ));

      // Test tablet device
      await tester.binding.setSurfaceSize(const Size(1024, 768));
      await tester.pumpAndSettle();

      final context = tester.element(find.byType(MaterialApp));

      // Run device performance test
      final testResult = await deviceTesting.runDevicePerformanceTest(context);
      
      expect(testResult, isNotNull);
      expect(testResult.deviceInfo.isTablet, isTrue);
      expect(testResult.testSuite.totalTests, greaterThan(0));
      expect(testResult.performanceReport.overallScore, greaterThan(0));
      
      print('✅ DeviceTestingFramework integration: PASSED');
      print('   - Device detected as tablet: ${testResult.deviceInfo.isTablet}');
      print('   - Tests run: ${testResult.testSuite.totalTests}');
      print('   - Tests passed: ${testResult.testSuite.passedTests}');
      print('   - Overall score: ${testResult.performanceReport.overallScore.toStringAsFixed(1)}');
    });

    testWidgets('✅ Performance Improvement Validator Integration', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(body: Container(child: Text('Test App'))),
      ));

      await tester.binding.setSurfaceSize(const Size(1024, 768));
      await tester.pumpAndSettle();

      final context = tester.element(find.byType(MaterialApp));

      // Run performance validation
      final validationResult = await validator.validatePerformanceImprovements(context);
      
      expect(validationResult, isNotNull);
      expect(validationResult.validationTests, isNotEmpty);
      expect(validationResult.improvementScore, greaterThan(0));
      
      print('✅ PerformanceImprovementValidator integration: PASSED');
      print('   - Validation tests: ${validationResult.validationTests.length}');
      print('   - Improvement score: ${validationResult.improvementScore.toStringAsFixed(1)}%');
      print('   - Targets achieved: ${validationResult.targetsAchieved}');
      
      // Check individual test results
      for (final test in validationResult.validationTests) {
        final status = test.targetAchieved ? '✅' : '⚠️';
        print('   $status ${test.name}: ${test.improvementPercentage.toStringAsFixed(1)}% improvement');
      }
    });

    testWidgets('✅ Performance Monitoring Dashboard Integration', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(body: Container(child: Text('Test App'))),
      ));

      await tester.binding.setSurfaceSize(const Size(1024, 768));
      await tester.pumpAndSettle();

      final context = tester.element(find.byType(MaterialApp));

      // Start dashboard monitoring
      await dashboard.startMonitoring(context);
      await tester.pump(const Duration(seconds: 2));
      
      final dashboardData = dashboard.getCurrentDashboardData();
      
      expect(dashboardData, isNotNull);
      expect(dashboardData.isMonitoring, isTrue);
      expect(dashboardData.currentSnapshot.deviceInfo.isTablet, isTrue);
      
      dashboard.stopMonitoring();
      
      print('✅ PerformanceMonitoringDashboard integration: PASSED');
      print('   - Monitoring active: ${dashboardData.isMonitoring}');
      print('   - Snapshots captured: ${dashboardData.totalSnapshots}');
      print('   - Form factor detected: ${dashboardData.currentSnapshot.deviceInfo.formFactor.name}');
    });

    testWidgets('✅ Orientation Change Monitor Integration', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(body: Container(child: Text('Test App'))),
      ));

      // Start in tablet landscape
      await tester.binding.setSurfaceSize(const Size(1024, 768));
      await tester.pumpAndSettle();

      final context = tester.element(find.byType(MaterialApp));
      orientationMonitor.initialize(context);

      // Simulate orientation change
      orientationMonitor.trackOrientationChangeStart(context);
      
      // Change to portrait
      await tester.binding.setSurfaceSize(const Size(768, 1024));
      await tester.pumpAndSettle();
      
      orientationMonitor.trackOrientationChangeComplete(context);
      
      final summary = orientationMonitor.getPerformanceSummary();
      
      expect(summary.totalChanges, greaterThanOrEqualTo(0));
      
      print('✅ OrientationChangeMonitor integration: PASSED');
      print('   - Total orientation changes: ${summary.totalChanges}');
      print('   - Average tablet change time: ${summary.averageTabletChangeTime.toStringAsFixed(1)}ms');
    });

    testWidgets('✅ ResponsiveSystem Cache Performance', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(body: Container(child: Text('Test App'))),
      ));

      await tester.binding.setSurfaceSize(const Size(1024, 768));
      await tester.pumpAndSettle();

      final context = tester.element(find.byType(MaterialApp));

      // Test cached ResponsiveSystem calls
      final stopwatch = Stopwatch()..start();
      
      for (int i = 0; i < 100; i++) {
        ResponsiveSystem.getAdaptiveTouchTargetSize(context);
        ResponsiveSystem.getAdaptiveColumnCount(context);
        ResponsiveSystem.getAdaptiveContentDensity(context);
      }
      
      stopwatch.stop();
      final averageQueryTime = stopwatch.elapsedMicroseconds / 100.0 / 1000.0; // ms per query
      
      expect(averageQueryTime, lessThan(5.0), reason: 'Cache should make queries fast');
      
      print('✅ ResponsiveSystem cache performance: PASSED');
      print('   - Average query time: ${averageQueryTime.toStringAsFixed(2)}ms');
      print('   - Performance target (<5ms): ${averageQueryTime < 5.0 ? 'MET' : 'NOT MET'}');
    });

    testWidgets('✅ Form Factor Detection Accuracy', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(body: Container(child: Text('Test App'))),
      ));

      final context = tester.element(find.byType(MaterialApp));

      // Test mobile detection
      await tester.binding.setSurfaceSize(const Size(375, 667)); // iPhone size
      await tester.pumpAndSettle();
      
      expect(DesignSystem.isTablet(context), isFalse, reason: 'Should detect mobile device');
      
      // Test tablet detection
      await tester.binding.setSurfaceSize(const Size(1024, 768)); // iPad size
      await tester.pumpAndSettle();
      
      expect(DesignSystem.isTablet(context), isTrue, reason: 'Should detect tablet device');
      
      print('✅ Form factor detection: PASSED');
      print('   - Mobile detection: ✅');
      print('   - Tablet detection: ✅');
    });

    test('✅ Performance Target Validation', () {
      // Validate our performance targets are realistic and achievable
      const targets = {
        'Orientation Change Time': 100.0, // ms
        'NavigationRail Optimization': 15.0, // % improvement
        'Memory Efficiency Gain': 20.0, // % improvement
        'Frame Rate Stability': 95.0, // % stable frames
        'Cache Hit Rate': 85.0, // %
      };

      for (final entry in targets.entries) {
        expect(entry.value, greaterThan(0), reason: '${entry.key} target should be positive');
        print('   - ${entry.key}: ${entry.value}${entry.key.contains('Time') ? 'ms' : '%'} target');
      }
      
      print('✅ Performance targets validation: PASSED');
    });
  });

  group('🔧 Production Readiness Checks', () {
    test('✅ All Performance Classes Available', () {
      // Verify all classes can be instantiated
      expect(() => PerformanceMetrics(), returnsNormally);
      expect(() => DeviceTestingFramework(), returnsNormally);
      expect(() => PerformanceImprovementValidator(), returnsNormally);
      expect(() => PerformanceMonitoringDashboard(), returnsNormally);
      expect(() => OrientationChangeMonitor(), returnsNormally);
      
      print('✅ All performance classes instantiable: PASSED');
    });

    test('✅ Performance Logging Integration', () {
      // Test that performance logging doesn't crash
      final performanceMetrics = PerformanceMetrics();
      
      expect(() => performanceMetrics.trackCustomEvent('test_event', {'key': 'value'}), returnsNormally);
      expect(() => performanceMetrics.trackNavigationLayoutSwitch(NavigationLayoutType.navigationRail, 10.0), returnsNormally);
      expect(() => performanceMetrics.trackResponsiveSystemQuery(true, 1.0), returnsNormally);
      
      print('✅ Performance logging integration: PASSED');
    });
  });
}
