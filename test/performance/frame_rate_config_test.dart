import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dasso_reader/utils/performance/frame_rate_monitor.dart';
import 'package:dasso_reader/config/design_system.dart';

void main() {
  group('FrameRateConfig Form Factor Awareness', () {
    test('should create default configuration when no context provided', () {
      // Test backward compatibility - formFactorAwareConfig should work without context
      final config = FrameRateConfig.formFactorAwareConfig();
      final defaultConfig = FrameRateConfig.defaultConfig();

      expect(config.targetFPS, equals(defaultConfig.targetFPS));
      expect(config.jankThresholdMicroseconds,
          equals(defaultConfig.jankThresholdMicroseconds));
      expect(config.frameHistorySize, equals(defaultConfig.frameHistorySize));
      expect(config.maxJankEvents, equals(defaultConfig.maxJankEvents));
    });

    testWidgets('should return mobile configuration for mobile devices',
        (tester) async {
      // Set mobile screen size explicitly
      await tester.binding.setSurfaceSize(const Size(400, 800));

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // Verify this is detected as mobile, then test configuration
              if (!DesignSystem.isTablet(context)) {
                final config = FrameRateConfig.formFactorAwareConfig(context);
                final defaultConfig = FrameRateConfig.defaultConfig();

                // Should match default mobile configuration
                expect(config.targetFPS, equals(defaultConfig.targetFPS));
                expect(config.jankThresholdMicroseconds,
                    equals(defaultConfig.jankThresholdMicroseconds));
                expect(config.frameHistorySize,
                    equals(defaultConfig.frameHistorySize));
                expect(
                    config.maxJankEvents, equals(defaultConfig.maxJankEvents));
              }
              return const SizedBox();
            },
          ),
        ),
      );

      // Reset to default size
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('should return tablet configuration for tablet devices',
        (tester) async {
      // Set tablet screen size
      await tester.binding.setSurfaceSize(const Size(800, 1200));

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // Only test if this is actually detected as a tablet
              if (DesignSystem.isTablet(context)) {
                final config = FrameRateConfig.formFactorAwareConfig(context);
                final tabletConfig = FrameRateConfig.tabletOptimized();

                // Should match tablet-optimized configuration
                expect(config.targetFPS, equals(tabletConfig.targetFPS));
                expect(config.jankThresholdMicroseconds,
                    equals(tabletConfig.jankThresholdMicroseconds));
                expect(config.frameHistorySize,
                    equals(tabletConfig.frameHistorySize));
                expect(
                    config.maxJankEvents, equals(tabletConfig.maxJankEvents));
              }
              return const SizedBox();
            },
          ),
        ),
      );

      // Reset to default size
      await tester.binding.setSurfaceSize(null);
    });

    test('should create tablet-optimized configuration with enhanced settings',
        () {
      final config = FrameRateConfig.tabletOptimized();

      // Tablet-specific optimizations
      expect(config.targetFPS, equals(60.0)); // Same target FPS
      expect(config.jankThresholdMicroseconds,
          equals(18000)); // 18ms (more lenient than 16.67ms)
      expect(config.frameHistorySize,
          equals(180)); // 3 seconds (50% more than default 120)
      expect(config.maxJankEvents, equals(150)); // 50% more than default 100
      expect(config.enableDebugLogging, isFalse);
      expect(config.enableJankLogging, isFalse);
    });

    test('should create mobile-optimized configuration matching default', () {
      final mobileConfig = FrameRateConfig.mobileOptimized();
      final defaultConfig = FrameRateConfig.defaultConfig();

      // Mobile config should match default
      expect(mobileConfig.targetFPS, equals(defaultConfig.targetFPS));
      expect(mobileConfig.jankThresholdMicroseconds,
          equals(defaultConfig.jankThresholdMicroseconds));
      expect(mobileConfig.frameHistorySize,
          equals(defaultConfig.frameHistorySize));
      expect(mobileConfig.maxJankEvents, equals(defaultConfig.maxJankEvents));
    });

    test('should create tablet high refresh rate configuration', () {
      final config = FrameRateConfig.tabletHighRefreshRate();

      // High refresh rate tablet settings
      expect(config.targetFPS, equals(120.0)); // 120 FPS target
      expect(config.jankThresholdMicroseconds,
          equals(9000)); // 9ms (more lenient than 8.33ms)
      expect(config.frameHistorySize, equals(360)); // 3 seconds at 120 FPS
      expect(config.maxJankEvents, equals(150)); // Enhanced jank tracking
      expect(config.enableDebugLogging, isFalse);
      expect(config.enableJankLogging, isFalse);
    });

    test('should maintain backward compatibility with existing factory methods',
        () {
      // All existing factory methods should continue to work unchanged
      final defaultConfig = FrameRateConfig.defaultConfig();
      expect(defaultConfig.targetFPS, equals(60.0));
      expect(defaultConfig.jankThresholdMicroseconds, equals(16670));
      expect(defaultConfig.frameHistorySize, equals(120));
      expect(defaultConfig.maxJankEvents, equals(100));

      final verboseConfig = FrameRateConfig.verboseConfig();
      expect(verboseConfig.enableDebugLogging, isTrue);
      expect(verboseConfig.enableJankLogging, isTrue);

      final highRefreshConfig = FrameRateConfig.highRefreshRate();
      expect(highRefreshConfig.targetFPS, equals(120.0));
      expect(highRefreshConfig.jankThresholdMicroseconds, equals(8330));
    });

    test(
        'should provide appropriate jank thresholds for different form factors',
        () {
      final mobileConfig = FrameRateConfig.defaultConfig();
      final tabletConfig = FrameRateConfig.tabletOptimized();
      final tabletHighRefresh = FrameRateConfig.tabletHighRefreshRate();

      // Verify threshold progression: mobile < tablet < tablet high refresh
      expect(mobileConfig.jankThresholdMicroseconds, equals(16670)); // 16.67ms
      expect(tabletConfig.jankThresholdMicroseconds,
          equals(18000)); // 18ms (more lenient)
      expect(tabletHighRefresh.jankThresholdMicroseconds,
          equals(9000)); // 9ms for 120 FPS

      // Verify frame history progression
      expect(mobileConfig.frameHistorySize, equals(120)); // 2 seconds at 60 FPS
      expect(tabletConfig.frameHistorySize, equals(180)); // 3 seconds at 60 FPS
      expect(tabletHighRefresh.frameHistorySize,
          equals(360)); // 3 seconds at 120 FPS
    });
  });
}
