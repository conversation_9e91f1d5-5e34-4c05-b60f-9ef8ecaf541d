import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dasso_reader/utils/performance/performance_optimizer.dart';
import 'package:dasso_reader/config/design_system.dart';

void main() {
  group('PerformanceOptimizer Form Factor Integration', () {
    late PerformanceOptimizer optimizer;

    setUp(() {
      optimizer = PerformanceOptimizer();
    });

    testWidgets(
        'should provide form factor-aware performance reports for mobile',
        (tester) async {
      // Set mobile screen size
      await tester.binding.setSurfaceSize(const Size(400, 800));

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // Verify this is detected as mobile
              if (!DesignSystem.isTablet(context)) {
                // Test form factor-aware report
                final report = optimizer.getFormFactorAwareReport(context);
                expect(report, isNotNull);

                // Should work without context too (backward compatibility)
                final standardReport = optimizer.getPerformanceReport();
                expect(standardReport, isNotNull);
              }
              return const SizedBox();
            },
          ),
        ),
      );

      // Reset to default size
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets(
        'should provide form factor-aware performance reports for tablet',
        (tester) async {
      // Set tablet screen size
      await tester.binding.setSurfaceSize(const Size(800, 1200));

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // Only test if this is actually detected as a tablet
              if (DesignSystem.isTablet(context)) {
                // Test form factor-aware report
                final report = optimizer.getFormFactorAwareReport(context);
                expect(report, isNotNull);
                expect(report.timestamp, isA<DateTime>());

                // Report should include form factor information in issue metrics
                // (when issues are present, they would include formFactor field)
              }
              return const SizedBox();
            },
          ),
        ),
      );

      // Reset to default size
      await tester.binding.setSurfaceSize(null);
    });

    test('should maintain backward compatibility with existing methods', () {
      // All existing methods should continue to work unchanged
      expect(() => optimizer.getPerformanceReport(), returnsNormally);

      // New method should work without context
      expect(() => optimizer.getFormFactorAwareReport(), returnsNormally);

      // Should return valid reports
      final standardReport = optimizer.getPerformanceReport();
      final awareReport = optimizer.getFormFactorAwareReport();

      expect(standardReport, isA<PerformanceReport>());
      expect(awareReport, isA<PerformanceReport>());
    });

    testWidgets('should use appropriate thresholds based on form factor',
        (tester) async {
      // This test verifies that the integration uses the correct thresholds
      // by checking that tablet and mobile contexts would produce different
      // threshold values in the performance analysis

      // Mobile context
      await tester.binding.setSurfaceSize(const Size(400, 800));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              if (!DesignSystem.isTablet(context)) {
                final mobileReport =
                    optimizer.getFormFactorAwareReport(context);
                expect(mobileReport, isNotNull);
                // Mobile report uses standard thresholds
              }
              return const SizedBox();
            },
          ),
        ),
      );

      // Tablet context
      await tester.binding.setSurfaceSize(const Size(800, 1200));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              if (DesignSystem.isTablet(context)) {
                final tabletReport =
                    optimizer.getFormFactorAwareReport(context);
                expect(tabletReport, isNotNull);
                // Tablet report uses enhanced thresholds
              }
              return const SizedBox();
            },
          ),
        ),
      );

      // Reset to default size
      await tester.binding.setSurfaceSize(null);
    });

    test('should handle null context gracefully', () {
      // Form factor-aware method should work without context
      expect(() => optimizer.getFormFactorAwareReport(null), returnsNormally);
      expect(() => optimizer.getFormFactorAwareReport(), returnsNormally);

      final report = optimizer.getFormFactorAwareReport();
      expect(report, isA<PerformanceReport>());
    });

    testWidgets('should include form factor information in issue metrics',
        (tester) async {
      // This test would verify that when performance issues are detected,
      // they include form factor information in their metrics

      await tester.binding.setSurfaceSize(const Size(800, 1200));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              if (DesignSystem.isTablet(context)) {
                final report = optimizer.getFormFactorAwareReport(context);

                // If there were performance issues, they would contain:
                // - 'formFactor': 'tablet' or 'mobile'
                // - Form factor-aware threshold values
                // - Enhanced descriptions with form factor context

                expect(report.issues, isA<List<dynamic>>());
              }
              return const SizedBox();
            },
          ),
        ),
      );

      // Reset to default size
      await tester.binding.setSurfaceSize(null);
    });
  });
}
