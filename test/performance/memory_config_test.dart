import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dasso_reader/utils/performance/memory_usage_monitor.dart';
import 'package:dasso_reader/config/design_system.dart';

void main() {
  group('MemoryConfig Form Factor Awareness', () {
    test('should return base memory limits when no context provided', () {
      // Test backward compatibility - existing method should work unchanged
      final baseLimit = MemoryConfig.getDeviceMemoryLimit();
      expect(baseLimit, equals(300)); // Mobile default for Android/iOS
    });

    testWidgets('should return base memory limits for mobile devices',
        (tester) async {
      // Set mobile screen size explicitly
      await tester.binding.setSurfaceSize(const Size(400, 800));

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // Verify this is detected as mobile, then test memory limits
              if (!DesignSystem.isTablet(context)) {
                final memoryLimit = MemoryConfig.getDeviceMemoryLimit(context);
                expect(memoryLimit, equals(300)); // Mobile memory limit
              }
              return const SizedBox();
            },
          ),
        ),
      );

      // Reset to default size
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('should return enhanced memory limits for tablet devices',
        (tester) async {
      // Set tablet screen size
      await tester.binding.setSurfaceSize(const Size(800, 1200));

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // Only test if this is actually detected as a tablet
              if (DesignSystem.isTablet(context)) {
                final memoryLimit = MemoryConfig.getDeviceMemoryLimit(context);
                expect(memoryLimit,
                    equals(500)); // Tablet memory limit (500MB vs 300MB mobile)
              }
              return const SizedBox();
            },
          ),
        ),
      );

      // Reset to default size
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('should create form factor-aware configuration for mobile',
        (tester) async {
      // Set mobile screen size
      await tester.binding.setSurfaceSize(const Size(400, 800));

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              if (!DesignSystem.isTablet(context)) {
                final config = MemoryConfig.formFactorAwareConfig(context);

                // Mobile-specific configuration
                expect(config.deviceMemoryLimitMB, equals(300));
                expect(config.memorySpikeThresholdMB,
                    equals(20)); // Standard spike threshold
                expect(config.maxHistorySize,
                    equals(100)); // Standard history size
                expect(config.memoryPressureThreshold, equals(80.0));
              }
              return const SizedBox();
            },
          ),
        ),
      );

      // Reset to default size
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('should create form factor-aware configuration for tablet',
        (tester) async {
      // Set tablet screen size
      await tester.binding.setSurfaceSize(const Size(800, 1200));

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              if (DesignSystem.isTablet(context)) {
                final config = MemoryConfig.formFactorAwareConfig(context);

                // Tablet-specific configuration
                expect(config.deviceMemoryLimitMB,
                    equals(500)); // Higher memory limit
                expect(config.memorySpikeThresholdMB,
                    equals(30)); // Higher spike threshold
                expect(config.maxHistorySize,
                    equals(150)); // More history for tablets
                expect(config.memoryPressureThreshold,
                    equals(80.0)); // Same pressure threshold
              }
              return const SizedBox();
            },
          ),
        ),
      );

      // Reset to default size
      await tester.binding.setSurfaceSize(null);
    });

    test('should maintain backward compatibility with existing defaultConfig',
        () {
      // Existing defaultConfig factory should continue to work unchanged
      final config = MemoryConfig.defaultConfig();

      expect(config.deviceMemoryLimitMB, equals(300)); // Mobile default
      expect(config.memoryPressureThreshold, equals(80.0));
      expect(config.memorySpikeThresholdMB, equals(20));
      expect(config.monitoringInterval, equals(const Duration(seconds: 5)));
      expect(config.maxHistorySize, equals(100));
      expect(config.maxSpikeHistory, equals(50));
      expect(config.enableAutomaticCleanup, isTrue);
      expect(config.minCleanupInterval, equals(const Duration(minutes: 1)));
    });

    testWidgets(
        'should create default config when no context provided to formFactorAwareConfig',
        (tester) async {
      // Should work without context (backward compatibility)
      final config = MemoryConfig.formFactorAwareConfig();

      expect(config.deviceMemoryLimitMB, equals(300)); // Mobile default
      expect(
          config.memorySpikeThresholdMB, equals(20)); // Mobile spike threshold
      expect(config.maxHistorySize, equals(100)); // Mobile history size
    });
  });
}
