import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dasso_reader/utils/performance/performance_optimizer.dart';
import 'package:dasso_reader/config/design_system.dart';

void main() {
  group('ProductionPerformanceConfig Form Factor Awareness', () {
    testWidgets('should return base thresholds when no context provided',
        (tester) async {
      // Test backward compatibility - all existing methods should work without context
      expect(ProductionPerformanceConfig.getJankRateThreshold(),
          equals(ProductionPerformanceConfig.jankRateThreshold));
      expect(ProductionPerformanceConfig.getMemoryPressureThreshold(),
          equals(ProductionPerformanceConfig.memoryPressureThreshold));
      expect(ProductionPerformanceConfig.getLowFPSThreshold(),
          equals(ProductionPerformanceConfig.lowFPSThreshold));
    });

    testWidgets('should return base thresholds for mobile devices',
        (tester) async {
      // Set mobile screen size explicitly
      await tester.binding.setSurfaceSize(const Size(400, 800));

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // Verify this is detected as mobile, then test thresholds
              if (!DesignSystem.isTablet(context)) {
                expect(
                    ProductionPerformanceConfig.getJankRateThreshold(context),
                    equals(ProductionPerformanceConfig.jankRateThreshold));
                expect(
                    ProductionPerformanceConfig.getMemoryPressureThreshold(
                        context),
                    equals(
                        ProductionPerformanceConfig.memoryPressureThreshold));
                expect(ProductionPerformanceConfig.getLowFPSThreshold(context),
                    equals(ProductionPerformanceConfig.lowFPSThreshold));
              }
              return const SizedBox();
            },
          ),
        ),
      );

      // Reset to default size
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('should return adjusted thresholds for tablet devices',
        (tester) async {
      // Set tablet screen size
      await tester.binding.setSurfaceSize(const Size(800, 1200));

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // Only test if this is actually detected as a tablet
              if (DesignSystem.isTablet(context)) {
                final baseJankThreshold =
                    ProductionPerformanceConfig.jankRateThreshold;
                final baseMemoryThreshold =
                    ProductionPerformanceConfig.memoryPressureThreshold;
                final baseFPSThreshold =
                    ProductionPerformanceConfig.lowFPSThreshold;

                // Test tablet-specific adjustments
                expect(
                    ProductionPerformanceConfig.getJankRateThreshold(context),
                    equals(baseJankThreshold * 1.2));
                expect(
                    ProductionPerformanceConfig.getMemoryPressureThreshold(
                        context),
                    equals(baseMemoryThreshold * 1.1));
                expect(ProductionPerformanceConfig.getLowFPSThreshold(context),
                    equals(baseFPSThreshold * 0.95));
              }
              return const SizedBox();
            },
          ),
        ),
      );

      // Reset to default size
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('should include form factor info in aware config',
        (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              final config =
                  ProductionPerformanceConfig.getFormFactorAwareConfig(context);

              // Should include all base config items
              expect(config.containsKey('buildMode'), isTrue);
              expect(config.containsKey('jankRateThreshold'), isTrue);
              expect(config.containsKey('memoryPressureThreshold'), isTrue);

              // Should include form factor specific items
              expect(config.containsKey('formFactor'), isTrue);
              expect(
                  config.containsKey('formFactorAwareJankThreshold'), isTrue);
              expect(
                  config.containsKey('formFactorAwareMemoryThreshold'), isTrue);
              expect(config.containsKey('formFactorAwareFPSThreshold'), isTrue);

              // Form factor should be either 'mobile' or 'tablet'
              expect(
                  ['mobile', 'tablet'].contains(config['formFactor']), isTrue);

              return const SizedBox();
            },
          ),
        ),
      );
    });

    test('should maintain backward compatibility with existing methods', () {
      // All existing static getters should continue to work
      expect(ProductionPerformanceConfig.jankRateThreshold, isA<double>());
      expect(
          ProductionPerformanceConfig.memoryPressureThreshold, isA<double>());
      expect(ProductionPerformanceConfig.lowFPSThreshold, isA<double>());
      expect(ProductionPerformanceConfig.optimizationInterval, isA<Duration>());
      expect(ProductionPerformanceConfig.enableDetailedLogging, isA<bool>());
      expect(ProductionPerformanceConfig.enableVerboseLogging, isA<bool>());

      // getCurrentConfig should still work
      final config = ProductionPerformanceConfig.getCurrentConfig();
      expect(config, isA<Map<String, dynamic>>());
      expect(config.containsKey('buildMode'), isTrue);
    });
  });
}
