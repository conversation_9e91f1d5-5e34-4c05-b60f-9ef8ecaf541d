import 'dart:ui';

import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/config/platform_adaptations.dart';
import 'package:dasso_reader/config/app_typography.dart';
import 'package:dasso_reader/dao/database.dart';
import 'package:dasso_reader/enums/sync_direction.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/page/home_page.dart';
import 'package:dasso_reader/page/home_page/notes_page.dart';
import 'package:dasso_reader/service/book_player/book_player_server.dart';
import 'package:dasso_reader/service/iap_service.dart';
import 'package:dasso_reader/service/tts/tts_handler.dart';
import 'package:dasso_reader/service/dictionary/dictionary_service.dart';
import 'package:dasso_reader/utils/env_var.dart';
import 'package:dasso_reader/utils/error/common.dart';
import 'package:dasso_reader/utils/get_path/get_base_path.dart';
import 'package:dasso_reader/utils/haptic/haptic_feedback.dart';
import 'package:dasso_reader/utils/log/common.dart';

import 'package:dasso_reader/utils/state_management/app_state_manager.dart';
import 'package:dasso_reader/utils/performance/performance_metrics.dart';
import 'package:dasso_reader/utils/performance/performance_logging_config.dart';
import 'package:dasso_reader/utils/performance/tablet_performance_monitor.dart';
import 'package:dasso_reader/utils/accessibility/accessibility_testing.dart';

import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/utils/orientation_manager.dart';
import 'package:dasso_reader/providers/anx_webdav.dart';
import 'package:audio_service/audio_service.dart';
import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:provider/provider.dart' as provider;

final navigatorKey = GlobalKey<NavigatorState>();
late AudioHandler audioHandler;

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Prefs().initPrefs();

  initBasePath();
  AnxLog.init();
  AnxError.init();
  await AnxHapticFeedback.init();

  // Initialize manufacturer detection integrated into design system for pixel-perfect UI consistency
  PerformanceMetrics()
      .trackStartupPhase('manufacturer_detection_initialization');
  try {
    await DesignSystem.initializeManufacturerDetection();
    AnxLog.info(
      '🎯 DesignSystem: Manufacturer detection initialized for pixel-perfect consistency',
    );
    AnxLog.info('📱 Device: ${DesignSystem.getDeviceDescription()}');
    AnxLog.info(
      '✅ Reference Device: ${DesignSystem.isReferenceDevice() ? 'YES (Pixel 9 Pro Standard)' : 'NO (Adjusted for consistency)'}',
    );
  } catch (e) {
    AnxLog.severe(
      '❌ DesignSystem manufacturer detection initialization failed: $e',
    );
    // Continue app initialization even if manufacturer detection fails
  }

  await DBHelper().initDB();

  // Initialize app state management system
  await AppStateManager().initialize();

  // Initialize performance monitoring
  PerformanceMetrics()
      .trackStartupPhase('orientation_management_initialization');

  // Note: We'll apply device-specific orientation constraints
  // in the app's root widget after context is available

  // Configure performance logging (quiet mode for cleaner development)
  // To enable performance debugging, change to:
  // PerformanceLoggingConfig.setMode(PerformanceLoggingMode.verbose);
  PerformanceLoggingConfig.setMode(PerformanceLoggingMode.quiet);

  // Initialize performance metrics system
  try {
    await PerformanceMetrics().initialize();
    PerformanceMetrics().startMonitoring();
    PerformanceMetrics().trackStartupPhase('main_initialization');

    // Initialize tablet performance monitoring
    await TabletPerformanceMonitor().initialize();

    // Only log in minimal mode or higher
    if (PerformanceLoggingConfig.enableInitializationLogging) {
      AnxLog.info('🚀 PerformanceMetrics: Initialized and monitoring');
      AnxLog.info('📊 TabletPerformanceMonitor: Initialized');
    }
  } catch (e, stackTrace) {
    AnxLog.severe('❌ PerformanceMetrics initialization failed: $e');
    AnxLog.severe('❌ Stack trace: $stackTrace');
  }

  if (EnvVar.isAppStore) {
    IAPService().initialize();
  }

  // Initialize dictionary service with LAZY loading (non-blocking)
  PerformanceMetrics().trackStartupPhase('dictionary_initialization');
  try {
    AnxLog.info('Starting lazy dictionary initialization');

    // Initialize dictionary service in background without blocking startup
    DictionaryService().initializeAsync().then((success) {
      if (success) {
        AnxLog.info('Dictionary service initialized successfully');

        // Start preloading if enabled
        if (Prefs().preloadDictionary) {
          AnxLog.info('Starting background dictionary preloading...');
          DictionaryService().preloadDictionary().then((preloadSuccess) {
            if (preloadSuccess) {
              AnxLog.info('Dictionary preloading completed successfully');
            } else {
              AnxLog.warning('Dictionary preloading failed');
            }
          }).catchError((Object e) {
            AnxLog.severe('Error during dictionary preloading: $e');
          });
        }
      } else {
        AnxLog.warning('Dictionary service initialization failed');
      }
    }).catchError((Object e) {
      AnxLog.severe('Error during dictionary initialization: $e');
    });

    // Mark phase as complete immediately (non-blocking)
    PerformanceMetrics().trackStartupPhase('dictionary_initialization_started');
  } catch (e) {
    AnxLog.severe('Error starting dictionary initialization: $e');
    // Continue app initialization even if dictionary fails
  }

  PerformanceMetrics().trackStartupPhase('server_initialization');
  Server().start();

  PerformanceMetrics().trackStartupPhase('audio_service_initialization');
  audioHandler = await AudioService.init(
    builder: () => TtsHandler(),
    config: const AudioServiceConfig(
      androidNotificationChannelId: 'com.anx.reader.tts.channel.audio',
      androidNotificationChannelName: 'ANX Reader TTS',
      androidNotificationOngoing: true,
      androidStopForegroundOnPause: true,
    ),
  );

  SmartDialog.config.custom = SmartConfigCustom(
    maskColor:
        DesignSystem.getStateLayerColor(Colors.black, 0.14), // 35/255 ≈ 0.14
    useAnimation: true,
    animationType: SmartAnimationType.centerFade_otherSlide,
  );

  PerformanceMetrics().trackStartupPhase('app_widget_creation');

  // Use optimized app initialization with mobile-proven patterns
  runApp(
    const ProviderScope(
      child: MyApp(),
    ),
  );

  // Mark startup completion after first frame
  WidgetsBinding.instance.addPostFrameCallback((_) {
    PerformanceMetrics().startupTracker.markFirstFrame();
    PerformanceMetrics().startupTracker.markStartupComplete();

    // Run accessibility validation in debug mode only
    assert(() {
      _runAccessibilityValidation();
      return true;
    }());
  });
}

/// Run accessibility validation in debug mode only
void _runAccessibilityValidation() {
  // Delay validation to ensure app is fully loaded
  Future.delayed(const Duration(seconds: 2), () {
    final context = navigatorKey.currentContext;
    if (context != null && context.mounted) {
      try {
        AnxLog.info('🧪 Starting Accessibility Validation...');

        final result = AccessibilityTesting.validateDassoReaderAccessibility(
          context: context,
        );

        // Log the validation report
        AnxLog.info('🎯 ACCESSIBILITY VALIDATION COMPLETE');
        AnxLog.info(result.summaryReport);

        // Use debug print for development visibility
        assert(() {
          final separator = '=' * 60;
          debugPrint('\n$separator');
          debugPrint(result.summaryReport);
          debugPrint('$separator\n');
          return true;
        }());
      } catch (e, stackTrace) {
        AnxLog.severe('❌ Accessibility validation failed: $e');
        AnxLog.severe('❌ Stack trace: $stackTrace');
      }
    }
  });
}

class MyApp extends ConsumerStatefulWidget {
  const MyApp({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Check dictionary preloading on app start
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkDictionaryPreloading();
      _applyGlobalOrientationConstraints();
      _startTabletPerformanceMonitoring();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    // Handle app state management first
    await AppStateManager().handleAppLifecycleChange(state);

    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.hidden) {
      if (Prefs().webdavStatus) {
        ref
            .read(anxWebdavProvider.notifier)
            .syncData(SyncDirection.upload, ref);
      }
    } else if (state == AppLifecycleState.resumed) {
      if (PlatformAdaptations.isIOS) {
        Server().start();
      }

      // Check dictionary preloading status when app is resumed
      _checkDictionaryPreloading();

      // Reapply orientation constraints when app is resumed
      // This ensures constraints are maintained after system changes
      _applyGlobalOrientationConstraints();
    }
  }

  // Helper method to check and initialize dictionary preloading if needed
  void _checkDictionaryPreloading() {
    if (Prefs().preloadDictionary &&
        Prefs().dictionaryPreloadStatus != 2 && // Not already loaded
        !DictionaryService().isPreloading) {
      // Not currently loading

      // Start preloading immediately
      DictionaryService().preloadDictionary().then((success) {
        if (!success) {
          AnxLog.warning('Dictionary preloading failed in UI check');
        }
      }).catchError((Object e) {
        AnxLog.severe('Error during dictionary preloading in UI check: $e');
      });
    }
  }

  /// Apply device-specific orientation constraints globally
  /// Mobile devices: Portrait-only for optimal Chinese reading experience
  /// Tablet devices: Both orientations for flexible reading positions
  void _applyGlobalOrientationConstraints() {
    if (mounted) {
      OrientationManager.applyOptimalOrientationConstraints(context);
    }
  }

  /// Start tablet performance monitoring with context
  void _startTabletPerformanceMonitoring() {
    if (mounted) {
      try {
        TabletPerformanceMonitor().startMonitoring(context);
        if (PerformanceLoggingConfig.enableInitializationLogging) {
          AnxLog.info('📊 TabletPerformanceMonitor: Started monitoring');
        }
      } catch (e) {
        AnxLog.severe('❌ TabletPerformanceMonitor start failed: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return provider.MultiProvider(
      providers: [
        provider.ChangeNotifierProvider(
          create: (_) => Prefs(),
        ),
        provider.ChangeNotifierProvider(
          create: (_) => NotesDetailModel(),
        ),
      ],
      child: provider.Consumer<Prefs>(
        builder: (context, prefsNotifier, child) {
          return Column(
            children: [
              Expanded(
                child: MaterialApp(
                  debugShowCheckedModeBanner: false,
                  scrollBehavior: ScrollConfiguration.of(context).copyWith(
                    physics: PlatformAdaptations.adaptiveScrollPhysics,
                    dragDevices: {
                      PointerDeviceKind.touch,
                      PointerDeviceKind.mouse,
                    },
                  ),
                  navigatorObservers: [FlutterSmartDialog.observer],
                  builder: FlutterSmartDialog.init(),
                  navigatorKey: navigatorKey,
                  locale: prefsNotifier.locale,
                  localizationsDelegates: L10n.localizationsDelegates,
                  supportedLocales: L10n.supportedLocales,
                  title: 'Anx',
                  themeMode: prefsNotifier.themeMode,
                  theme: _buildLightTheme(context, prefsNotifier),
                  darkTheme: _buildDarkTheme(context, prefsNotifier),
                  home: const HomePage(),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// Build light theme with E-ink mode support
  ThemeData _buildLightTheme(BuildContext context, Prefs prefsNotifier) {
    // Check for E-ink mode first
    if (prefsNotifier.eInkMode) {
      const eInkColorScheme = ColorScheme.light(
        primary: Colors.black,
        onPrimary: Colors.white,
        secondary: Colors.grey,
        onSecondary: Colors.white,
        surface: Colors.white,
        onSurface: Colors.black,
        primaryContainer: Colors.grey,
        onPrimaryContainer: Colors.black,
      );

      return FlexThemeData.light(
        useMaterial3: true,
        swapLegacyOnMaterial3: true,
        colorScheme: eInkColorScheme,
      )
          .copyWith(
            textTheme: AppTypography.getTextTheme(context),
            // Fix Android navigation regression: ensure proper slide transitions
            pageTransitionsTheme: const PageTransitionsTheme(
              builders: {
                TargetPlatform.android: CupertinoPageTransitionsBuilder(),
                TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
              },
            ),
          )
          .useSystemChineseFont(Brightness.light);
    }

    // Use simple color scheme from theme color
    final colorScheme = ColorScheme.fromSeed(
      seedColor: prefsNotifier.themeColor,
      brightness: Brightness.light,
    );

    return FlexThemeData.light(
      useMaterial3: true,
      swapLegacyOnMaterial3: true,
      colorScheme: colorScheme,
    )
        .copyWith(
          textTheme: AppTypography.getTextTheme(context),
          // Fix Android navigation regression: ensure proper slide transitions
          pageTransitionsTheme: const PageTransitionsTheme(
            builders: {
              TargetPlatform.android: CupertinoPageTransitionsBuilder(),
              TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
            },
          ),
        )
        .useSystemChineseFont(Brightness.light);
  }

  /// Build dark theme (simplified)
  ThemeData _buildDarkTheme(BuildContext context, Prefs prefsNotifier) {
    // E-ink mode uses light theme, so skip for dark theme

    final colorScheme = ColorScheme.fromSeed(
      seedColor: prefsNotifier.themeColor,
      brightness: Brightness.dark,
    );

    return FlexThemeData.dark(
      useMaterial3: true,
      swapLegacyOnMaterial3: true,
      darkIsTrueBlack: prefsNotifier.trueDarkMode,
      colorScheme: colorScheme,
    )
        .copyWith(
          textTheme: AppTypography.getTextTheme(context),
          // Fix Android navigation regression: ensure proper slide transitions
          pageTransitionsTheme: const PageTransitionsTheme(
            builders: {
              TargetPlatform.android: CupertinoPageTransitionsBuilder(),
              TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
            },
          ),
        )
        .useSystemChineseFont(Brightness.dark);
  }
}
