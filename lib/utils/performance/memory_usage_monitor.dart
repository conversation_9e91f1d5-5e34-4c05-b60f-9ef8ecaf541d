import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:dasso_reader/config/platform_adaptations.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Memory usage monitoring and optimization system
///
/// This system provides:
/// - Real-time memory tracking with device-specific limits
/// - Memory pressure detection and automatic cleanup
/// - Memory spike detection and analysis
/// - Large object tracking and disposal automation
class MemoryUsageMonitor {
  bool _isInitialized = false;
  bool _isMonitoring = false;
  MemoryConfig _config = MemoryConfig.defaultConfig();
  Timer? _monitoringTimer;

  // Memory tracking data
  int _currentMemoryUsageMB = 0;
  int _peakMemoryUsageMB = 0;
  final List<MemorySnapshot> _memoryHistory = [];
  final List<MemorySpike> _memorySpikes = [];

  // Large object tracking
  final Map<String, LargeObjectInfo> _largeObjects = {};

  // Memory pressure detection
  bool _isMemoryPressure = false;
  DateTime? _lastCleanupTime;

  // Native implementation tracking
  bool _nativeImplementationWorking = false;
  bool _warningShown = false;

  /// Initialize the memory usage monitor
  Future<void> initialize(MemoryConfig config) async {
    if (_isInitialized) return;

    _config = config;
    _isInitialized = true;

    // Get initial memory reading
    await _updateMemoryUsage();

    if (kDebugMode) {
      AnxLog.info(
        '💾 MemoryUsageMonitor: Initialized with ${_config.deviceMemoryLimitMB}MB limit',
      );
    }
  }

  /// Start memory usage monitoring
  void startMonitoring() {
    if (!_isInitialized || _isMonitoring) return;

    _isMonitoring = true;
    _setupPeriodicMonitoring();

    if (kDebugMode) {
      AnxLog.info('💾 MemoryUsageMonitor: Started monitoring');
    }
  }

  /// Stop memory usage monitoring
  void stopMonitoring() {
    if (!_isMonitoring) return;

    _isMonitoring = false;
    _monitoringTimer?.cancel();
    _monitoringTimer = null;

    if (kDebugMode) {
      AnxLog.info('💾 MemoryUsageMonitor: Stopped monitoring');
    }
  }

  /// Get current memory metrics
  MemoryMetrics getMetrics() {
    return MemoryMetrics(
      currentUsageMB: _currentMemoryUsageMB,
      peakUsageMB: _peakMemoryUsageMB,
      deviceLimitMB: _config.deviceMemoryLimitMB,
      memoryHistory: List.from(_memoryHistory),
      memorySpikes: List.from(_memorySpikes),
      largeObjectCount: _largeObjects.length,
      isMemoryPressure: _isMemoryPressure,
      performanceScore: _calculatePerformanceScore(),
    );
  }

  /// Track a large object
  void trackLargeObject(
    String id,
    String type,
    int sizeMB, {
    Map<String, dynamic>? metadata,
  }) {
    final info = LargeObjectInfo(
      id: id,
      type: type,
      sizeMB: sizeMB,
      createdAt: DateTime.now(),
      metadata: metadata ?? {},
    );

    _largeObjects[id] = info;

    if (kDebugMode && _config.enableLargeObjectLogging) {
      AnxLog.info('💾 Large Object Tracked: $type ($id) - ${sizeMB}MB');
    }

    // Check if this causes memory pressure
    _checkMemoryPressure();
  }

  /// Untrack a large object when disposed
  void untrackLargeObject(String id) {
    final info = _largeObjects.remove(id);

    if (info != null && kDebugMode && _config.enableLargeObjectLogging) {
      AnxLog.info(
        '💾 Large Object Disposed: ${info.type} (${info.id}) - ${info.sizeMB}MB',
      );
    }
  }

  /// Force memory cleanup
  Future<void> forceCleanup() async {
    if (kDebugMode) {
      AnxLog.info('💾 Forcing memory cleanup...');
    }

    // Trigger garbage collection
    await _triggerGarbageCollection();

    // Update memory usage
    await _updateMemoryUsage();

    _lastCleanupTime = DateTime.now();

    if (kDebugMode) {
      AnxLog.info(
        '💾 Memory cleanup completed. Current usage: ${_currentMemoryUsageMB}MB',
      );
    }
  }

  /// Get current memory usage in MB
  int get currentMemoryUsageMB => _currentMemoryUsageMB;

  /// Check if currently under memory pressure
  bool get isMemoryPressure => _isMemoryPressure;

  /// Setup periodic memory monitoring
  void _setupPeriodicMonitoring() {
    _monitoringTimer = Timer.periodic(_config.monitoringInterval, (_) async {
      await _updateMemoryUsage();
      _checkMemoryPressure();
      _maintainHistory();
    });
  }

  /// Update current memory usage
  Future<void> _updateMemoryUsage() async {
    try {
      // Get memory info from platform
      final memoryInfo = await _getMemoryInfo();
      final newUsageMB = memoryInfo['usedMemoryMB'] as int? ?? 0;

      // Update current usage
      final previousUsage = _currentMemoryUsageMB;
      _currentMemoryUsageMB = newUsageMB;

      // Update peak usage
      if (_currentMemoryUsageMB > _peakMemoryUsageMB) {
        _peakMemoryUsageMB = _currentMemoryUsageMB;
      }

      // Create memory snapshot
      final snapshot = MemorySnapshot(
        timestamp: DateTime.now(),
        usageMB: _currentMemoryUsageMB,
        largeObjectCount: _largeObjects.length,
      );

      _memoryHistory.add(snapshot);

      // Check for memory spikes
      _checkForMemorySpike(previousUsage, _currentMemoryUsageMB);
    } catch (error) {
      if (kDebugMode) {
        AnxLog.warning('💾 Failed to update memory usage: $error');
      }
    }
  }

  /// Get memory information from platform with enhanced error handling
  Future<Map<String, dynamic>> _getMemoryInfo() async {
    try {
      if (PlatformAdaptations.isAndroid) {
        // Use platform channel to get accurate memory info with timeout
        const platform = MethodChannel('dasso_reader/memory');
        final result = await platform
            .invokeMethod('getMemoryInfo')
            .timeout(const Duration(seconds: 2));

        // Validate result structure
        if (result is Map && result.containsKey('usedMemoryMB')) {
          // Success! Native implementation working
          if (kDebugMode && !_nativeImplementationWorking) {
            AnxLog.info('💾 Native memory monitoring now available');
            _nativeImplementationWorking = true;
          }
          return Map<String, dynamic>.from(result);
        } else {
          if (kDebugMode) {
            AnxLog.warning(
              '💾 Invalid platform channel response format: $result',
            );
          }
          return {'usedMemoryMB': _estimateMemoryUsage()};
        }
      } else {
        // Fallback for other platforms (iOS, Desktop, Web)
        return {'usedMemoryMB': _estimateMemoryUsage()};
      }
    } on TimeoutException {
      if (kDebugMode) {
        AnxLog.warning('💾 Platform channel timeout - using estimation');
      }
      return {'usedMemoryMB': _estimateMemoryUsage()};
    } on PlatformException catch (e) {
      if (kDebugMode) {
        AnxLog.warning('💾 Platform channel error: ${e.code} - ${e.message}');
      }
      return {'usedMemoryMB': _estimateMemoryUsage()};
    } catch (error) {
      // Only show warning once per session to avoid spam
      if (kDebugMode && !_warningShown) {
        AnxLog.warning(
          '💾 Memory monitoring using estimation (native implementation not available)',
        );
        _warningShown = true;
      }
      // Fallback to estimation
      return {'usedMemoryMB': _estimateMemoryUsage()};
    }
  }

  /// Estimate memory usage (fallback method)
  int _estimateMemoryUsage() {
    // Basic estimation based on large objects and base app usage
    final largeObjectMemory =
        _largeObjects.values.fold<int>(0, (sum, obj) => sum + obj.sizeMB);

    // Estimate base app memory usage
    const baseAppMemoryMB = 50; // Base Flutter app memory

    return baseAppMemoryMB + largeObjectMemory;
  }

  /// Check for memory pressure and trigger cleanup if needed
  void _checkMemoryPressure() {
    final usagePercent =
        (_currentMemoryUsageMB / _config.deviceMemoryLimitMB) * 100;
    final wasMemoryPressure = _isMemoryPressure;

    _isMemoryPressure = usagePercent >= _config.memoryPressureThreshold;

    if (_isMemoryPressure && !wasMemoryPressure) {
      if (kDebugMode) {
        AnxLog.warning(
          '💾 Memory pressure detected: ${usagePercent.toStringAsFixed(1)}%',
        );
      }

      // Trigger automatic cleanup if enabled
      if (_config.enableAutomaticCleanup) {
        _scheduleAutomaticCleanup();
      }
    } else if (!_isMemoryPressure && wasMemoryPressure) {
      if (kDebugMode) {
        AnxLog.info(
          '💾 Memory pressure relieved: ${usagePercent.toStringAsFixed(1)}%',
        );
      }
    }
  }

  /// Check for memory spikes with enhanced analysis
  void _checkForMemorySpike(int previousUsage, int currentUsage) {
    final increase = currentUsage - previousUsage;

    if (increase >= _config.memorySpikeThresholdMB) {
      final spike = MemorySpike(
        timestamp: DateTime.now(),
        previousUsageMB: previousUsage,
        currentUsageMB: currentUsage,
        increaseMB: increase,
        severity: _getSpikeSeverity(increase),
      );

      _memorySpikes.add(spike);

      // Maintain spike history
      while (_memorySpikes.length > _config.maxSpikeHistory) {
        _memorySpikes.removeAt(0);
      }

      if (kDebugMode) {
        _logMemorySpikeWithAnalysis(spike);
      }

      // Trigger cleanup for severe spikes
      if (spike.severity == SpikeSeverity.severe) {
        _scheduleAutomaticCleanup();
      }
    }
  }

  /// Log memory spike with detailed analysis and suggestions
  void _logMemorySpikeWithAnalysis(MemorySpike spike) {
    String analysis = '';
    String suggestion = '';

    // Analyze spike patterns
    final recentSpikes = _memorySpikes
        .where((s) => spike.timestamp.difference(s.timestamp).inMinutes < 5)
        .length;

    if (recentSpikes > 3) {
      analysis = ' [PATTERN: $recentSpikes spikes in 5min - possible leak]';
    }

    // Provide severity-based suggestions
    switch (spike.severity) {
      case SpikeSeverity.severe:
        suggestion =
            ' 💡 Check: large image loads, database operations, or memory leaks';
        break;
      case SpikeSeverity.moderate:
        suggestion =
            ' 💡 Consider: optimizing caches or reducing object retention';
        break;
      case SpikeSeverity.mild:
        if (recentSpikes > 2) {
          suggestion =
              ' 💡 Monitor: frequent small spikes may indicate gradual leak';
        }
        break;
    }

    // Calculate usage percentage
    final usagePercent = (_config.deviceMemoryLimitMB > 0)
        ? (spike.currentUsageMB / _config.deviceMemoryLimitMB * 100)
        : 0.0;

    AnxLog.warning(
        '💾 Memory spike detected: +${spike.increaseMB}MB (${spike.severity.name}) '
        'Total: ${spike.currentUsageMB}MB (${usagePercent.toStringAsFixed(1)}%)$analysis$suggestion');
  }

  /// Get memory spike severity
  SpikeSeverity _getSpikeSeverity(int increaseMB) {
    if (increaseMB >= _config.memorySpikeThresholdMB * 3) {
      return SpikeSeverity.severe;
    }
    if (increaseMB >= _config.memorySpikeThresholdMB * 2) {
      return SpikeSeverity.moderate;
    }
    return SpikeSeverity.mild;
  }

  /// Schedule automatic cleanup
  void _scheduleAutomaticCleanup() {
    // Avoid too frequent cleanups
    if (_lastCleanupTime != null &&
        DateTime.now().difference(_lastCleanupTime!) <
            _config.minCleanupInterval) {
      return;
    }

    // Schedule cleanup on next frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      forceCleanup();
    });
  }

  /// Trigger garbage collection
  Future<void> _triggerGarbageCollection() async {
    // Force garbage collection
    for (int i = 0; i < 3; i++) {
      await Future<void>.delayed(const Duration(milliseconds: 10));
      // Trigger GC by creating and discarding objects
      List.generate(1000, (index) => Object()).clear();
    }
  }

  /// Maintain memory history within limits
  void _maintainHistory() {
    while (_memoryHistory.length > _config.maxHistorySize) {
      _memoryHistory.removeAt(0);
    }
  }

  /// Calculate performance score (0-100)
  double _calculatePerformanceScore() {
    if (_config.deviceMemoryLimitMB == 0) return 100.0;

    final usagePercent =
        (_currentMemoryUsageMB / _config.deviceMemoryLimitMB) * 100;
    final usageScore = (100 - usagePercent).clamp(0.0, 100.0);

    // Penalize for memory spikes
    final spikePenalty = _memorySpikes.length * 2.0;

    // Penalize for memory pressure
    final pressurePenalty = _isMemoryPressure ? 20.0 : 0.0;

    return (usageScore - spikePenalty - pressurePenalty).clamp(0.0, 100.0);
  }

  /// Dispose of resources
  void dispose() {
    stopMonitoring();
    _memoryHistory.clear();
    _memorySpikes.clear();
    _largeObjects.clear();
    _isInitialized = false;

    if (kDebugMode) {
      AnxLog.info('💾 MemoryUsageMonitor: Disposed');
    }
  }
}

/// Memory usage monitoring configuration
class MemoryConfig {
  const MemoryConfig({
    required this.deviceMemoryLimitMB,
    required this.memoryPressureThreshold,
    required this.memorySpikeThresholdMB,
    required this.monitoringInterval,
    required this.maxHistorySize,
    required this.maxSpikeHistory,
    required this.enableAutomaticCleanup,
    required this.minCleanupInterval,
    required this.enableLargeObjectLogging,
  });

  final int deviceMemoryLimitMB;
  final double memoryPressureThreshold; // Percentage (0-100)
  final int memorySpikeThresholdMB;
  final Duration monitoringInterval;
  final int maxHistorySize;
  final int maxSpikeHistory;
  final bool enableAutomaticCleanup;
  final Duration minCleanupInterval;
  final bool enableLargeObjectLogging;

  factory MemoryConfig.defaultConfig() {
    return MemoryConfig(
      deviceMemoryLimitMB: _getDeviceMemoryLimit(),
      memoryPressureThreshold: 80.0, // 80% of device limit
      memorySpikeThresholdMB: 20, // 20MB spike threshold
      monitoringInterval: const Duration(seconds: 5),
      maxHistorySize: 100,
      maxSpikeHistory: 50,
      enableAutomaticCleanup: true,
      minCleanupInterval: const Duration(minutes: 1),
      enableLargeObjectLogging: kDebugMode,
    );
  }

  static int _getDeviceMemoryLimit() {
    // Estimate device memory limits based on platform
    if (PlatformAdaptations.isAndroid || PlatformAdaptations.isIOS) {
      // Conservative estimates for mobile devices
      return 300; // 300MB for mid-range devices
    } else {
      // Desktop/web platforms
      return 500; // 500MB for desktop
    }
  }

  // =====================================================
  // FORM FACTOR-AWARE MEMORY CONFIGURATION
  // =====================================================

  /// Form factor-aware device memory limit
  /// Tablets typically have more memory than mobile devices
  static int getDeviceMemoryLimit([BuildContext? context]) {
    if (context != null && DesignSystem.isTablet(context)) {
      // Tablets typically have more memory available
      if (PlatformAdaptations.isAndroid || PlatformAdaptations.isIOS) {
        return 500; // 500MB for tablets (vs 300MB for mobile)
      } else {
        return 750; // 750MB for desktop tablets
      }
    }
    return _getDeviceMemoryLimit(); // Use existing method for backward compatibility
  }

  /// Create form factor-aware memory configuration
  factory MemoryConfig.formFactorAwareConfig([BuildContext? context]) {
    return MemoryConfig(
      deviceMemoryLimitMB: getDeviceMemoryLimit(context),
      memoryPressureThreshold: 80.0, // 80% of device limit
      memorySpikeThresholdMB: context != null && DesignSystem.isTablet(context)
          ? 30 // Higher spike threshold for tablets (30MB vs 20MB)
          : 20, // Standard spike threshold for mobile
      monitoringInterval: const Duration(seconds: 5),
      maxHistorySize: context != null && DesignSystem.isTablet(context)
          ? 150 // More history for tablets (better memory)
          : 100, // Standard history for mobile
      maxSpikeHistory: 50,
      enableAutomaticCleanup: true,
      minCleanupInterval: const Duration(minutes: 1),
      enableLargeObjectLogging: kDebugMode,
    );
  }
}

/// Memory metrics data
class MemoryMetrics {
  const MemoryMetrics({
    required this.currentUsageMB,
    required this.peakUsageMB,
    required this.deviceLimitMB,
    required this.memoryHistory,
    required this.memorySpikes,
    required this.largeObjectCount,
    required this.isMemoryPressure,
    required this.performanceScore,
  });

  final int currentUsageMB;
  final int peakUsageMB;
  final int deviceLimitMB;
  final List<MemorySnapshot> memoryHistory;
  final List<MemorySpike> memorySpikes;
  final int largeObjectCount;
  final bool isMemoryPressure;
  final double performanceScore;

  /// Get memory usage percentage
  double get usagePercent =>
      deviceLimitMB > 0 ? (currentUsageMB / deviceLimitMB * 100) : 0.0;

  /// Check if memory usage is acceptable
  bool get isMemoryUsageAcceptable => performanceScore >= 70.0;

  /// Get average memory usage from history
  double get averageUsageMB {
    if (memoryHistory.isEmpty) return currentUsageMB.toDouble();

    final total =
        memoryHistory.fold<int>(0, (sum, snapshot) => sum + snapshot.usageMB);
    return total / memoryHistory.length;
  }
}

/// Memory snapshot data
class MemorySnapshot {
  const MemorySnapshot({
    required this.timestamp,
    required this.usageMB,
    required this.largeObjectCount,
  });

  final DateTime timestamp;
  final int usageMB;
  final int largeObjectCount;
}

/// Memory spike data
class MemorySpike {
  const MemorySpike({
    required this.timestamp,
    required this.previousUsageMB,
    required this.currentUsageMB,
    required this.increaseMB,
    required this.severity,
  });

  final DateTime timestamp;
  final int previousUsageMB;
  final int currentUsageMB;
  final int increaseMB;
  final SpikeSeverity severity;

  /// Get spike percentage increase
  double get increasePercent =>
      previousUsageMB > 0 ? (increaseMB / previousUsageMB * 100) : 0.0;
}

/// Large object tracking information
class LargeObjectInfo {
  const LargeObjectInfo({
    required this.id,
    required this.type,
    required this.sizeMB,
    required this.createdAt,
    required this.metadata,
  });

  final String id;
  final String type;
  final int sizeMB;
  final DateTime createdAt;
  final Map<String, dynamic> metadata;

  /// Get object age
  Duration get age => DateTime.now().difference(createdAt);
}

/// Memory spike severity levels
enum SpikeSeverity {
  mild,
  moderate,
  severe,
}
