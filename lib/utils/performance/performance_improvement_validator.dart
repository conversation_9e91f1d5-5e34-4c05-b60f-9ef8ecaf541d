import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/performance/performance_metrics.dart';
import 'package:dasso_reader/utils/performance/device_testing_framework.dart';
import 'package:dasso_reader/utils/performance/orientation_change_monitor.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';

/// Performance improvement validation system
/// Validates 15-20% performance improvement targets across tablet optimizations
class PerformanceImprovementValidator {
  static final PerformanceImprovementValidator _instance =
      PerformanceImprovementValidator._internal();
  factory PerformanceImprovementValidator() => _instance;
  PerformanceImprovementValidator._internal();

  // Validation state
  bool _isValidating = false;
  final List<ValidationResult> _validationHistory = [];

  // Performance targets (15-20% improvement goals)
  static const PerformanceTargets _targets = PerformanceTargets(
    orientationChangeTime: 100.0, // <100ms target
    navigationRailOptimization: 15.0, // 15% improvement
    memoryEfficiencyGain: 20.0, // 20% improvement
    overallPerformanceImprovement: 17.5, // 17.5% average improvement
    frameRateStability: 95.0, // 95% stable frames
    responsiveSystemCacheHitRate: 85.0, // 85% cache hit rate
  );

  // Baseline performance metrics (simulated pre-optimization values)
  static const BaselineMetrics _baseline = BaselineMetrics(
    orientationChangeTime: 150.0, // Pre-optimization: 150ms average
    navigationRailRenderTime: 20.0, // Pre-optimization: 20ms
    memoryUsagePercentage: 75.0, // Pre-optimization: 75% usage
    frameRateAverage: 52.0, // Pre-optimization: 52fps
    responsiveSystemQueryTime: 5.0, // Pre-optimization: 5ms per query
  );

  /// Initialize the validation system
  Future<void> initialize() async {
    if (kDebugMode) {
      AnxLog.info('🎯 PerformanceImprovementValidator initialized');
    }
  }

  /// Validate comprehensive performance improvements
  Future<ValidationResult> validatePerformanceImprovements(
      BuildContext context) async {
    if (_isValidating) {
      throw StateError('Performance validation is already running');
    }

    _isValidating = true;
    final startTime = DateTime.now();

    try {
      if (kDebugMode) {
        AnxLog.info('🎯 Starting performance improvement validation...');
      }

      // Run comprehensive device test
      final deviceTestingFramework = DeviceTestingFramework();
      await deviceTestingFramework.initialize();
      final deviceTestResult =
          await deviceTestingFramework.runDevicePerformanceTest(context);

      // Validate specific improvement targets
      final validationTests =
          await _runValidationTests(context, deviceTestResult);

      // Calculate overall improvement score
      final improvementScore =
          _calculateOverallImprovementScore(validationTests);

      final endTime = DateTime.now();
      final validationDuration = endTime.difference(startTime);

      final result = ValidationResult(
        deviceTestResult: deviceTestResult,
        validationTests: validationTests,
        improvementScore: improvementScore,
        targetsAchieved:
            improvementScore >= _targets.overallPerformanceImprovement,
        validationDuration: validationDuration,
        timestamp: endTime,
      );

      // Store result
      _validationHistory.add(result);
      if (_validationHistory.length > 50) {
        _validationHistory.removeAt(0);
      }

      if (kDebugMode) {
        final status = result.targetsAchieved ? 'ACHIEVED' : 'NOT ACHIEVED';
        AnxLog.info(
            '🎯 Performance validation completed: $status (${improvementScore.toStringAsFixed(1)}% improvement)');
      }

      return result;
    } finally {
      _isValidating = false;
    }
  }

  /// Run specific validation tests for improvement targets
  Future<List<ImprovementValidationTest>> _runValidationTests(
    BuildContext context,
    DeviceTestResult deviceTestResult,
  ) async {
    final tests = <ImprovementValidationTest>[];

    // Test 1: Orientation Change Performance Improvement
    tests.add(await _validateOrientationChangeImprovement(context));

    // Test 2: NavigationRail Optimization Improvement
    tests.add(await _validateNavigationRailOptimization(deviceTestResult));

    // Test 3: Memory Efficiency Improvement
    tests.add(await _validateMemoryEfficiencyImprovement(deviceTestResult));

    // Test 4: Frame Rate Stability Improvement
    tests.add(await _validateFrameRateStabilityImprovement(deviceTestResult));

    // Test 5: ResponsiveSystem Cache Performance Improvement
    tests.add(await _validateResponsiveSystemCacheImprovement(context));

    return tests;
  }

  /// Validate orientation change performance improvement
  Future<ImprovementValidationTest> _validateOrientationChangeImprovement(
      BuildContext context) async {
    final orientationMonitor = OrientationChangeMonitor();
    final summary = orientationMonitor.getPerformanceSummary();

    final currentTime = summary.averageTabletChangeTime;
    final improvement =
        _calculateImprovement(_baseline.orientationChangeTime, currentTime);
    final targetAchieved = currentTime <= _targets.orientationChangeTime;

    return ImprovementValidationTest(
      name: 'Orientation Change Performance',
      description: 'Validates orientation change time improvements',
      baselineValue: _baseline.orientationChangeTime,
      currentValue: currentTime,
      targetValue: _targets.orientationChangeTime,
      improvementPercentage: improvement,
      targetAchieved: targetAchieved,
      metrics: {
        'averageChangeTime': currentTime,
        'slowChangePercentage': summary.slowTabletChangePercentage,
        'totalChanges': summary.totalChanges,
      },
    );
  }

  /// Validate NavigationRail optimization improvement
  Future<ImprovementValidationTest> _validateNavigationRailOptimization(
      DeviceTestResult deviceTestResult) async {
    // Extract NavigationRail performance from device test
    final navigationTest = deviceTestResult.testSuite.tests
        .firstWhere((test) => test.name == 'Navigation Layout Performance');

    final currentRenderTime = navigationTest.metrics['renderTime'] as double;
    final improvement = _calculateImprovement(
        _baseline.navigationRailRenderTime, currentRenderTime);
    final targetAchieved = improvement >= _targets.navigationRailOptimization;

    return ImprovementValidationTest(
      name: 'NavigationRail Optimization',
      description:
          'Validates NavigationRail rendering performance improvements',
      baselineValue: _baseline.navigationRailRenderTime,
      currentValue: currentRenderTime,
      targetValue: _baseline.navigationRailRenderTime *
          (1 - _targets.navigationRailOptimization / 100),
      improvementPercentage: improvement,
      targetAchieved: targetAchieved,
      metrics: {
        'renderTime': currentRenderTime,
        'layoutType': navigationTest.metrics['layoutType'],
        'targetFPS': navigationTest.metrics['targetFPS'],
      },
    );
  }

  /// Validate memory efficiency improvement
  Future<ImprovementValidationTest> _validateMemoryEfficiencyImprovement(
      DeviceTestResult deviceTestResult) async {
    // Extract memory performance from device test
    final memoryTest = deviceTestResult.testSuite.tests
        .firstWhere((test) => test.name == 'Memory Efficiency');

    final currentUsagePercentage =
        memoryTest.metrics['usagePercentage'] as double;
    final improvement = _calculateImprovement(
        _baseline.memoryUsagePercentage, currentUsagePercentage);
    final targetAchieved = improvement >= _targets.memoryEfficiencyGain;

    return ImprovementValidationTest(
      name: 'Memory Efficiency',
      description: 'Validates memory usage efficiency improvements',
      baselineValue: _baseline.memoryUsagePercentage,
      currentValue: currentUsagePercentage,
      targetValue: _baseline.memoryUsagePercentage *
          (1 - _targets.memoryEfficiencyGain / 100),
      improvementPercentage: improvement,
      targetAchieved: targetAchieved,
      metrics: {
        'currentUsageMB': memoryTest.metrics['currentUsageMB'],
        'deviceLimitMB': memoryTest.metrics['deviceLimitMB'],
        'usagePercentage': currentUsagePercentage,
        'isMemoryPressure': memoryTest.metrics['isMemoryPressure'],
      },
    );
  }

  /// Validate frame rate stability improvement
  Future<ImprovementValidationTest> _validateFrameRateStabilityImprovement(
      DeviceTestResult deviceTestResult) async {
    // Extract frame rate performance from device test
    final frameRateTest = deviceTestResult.testSuite.tests
        .firstWhere((test) => test.name == 'Frame Rate Stability');

    final currentFPS = frameRateTest.metrics['averageFPS'] as double;
    final improvement =
        _calculateImprovement(_baseline.frameRateAverage, currentFPS);
    final targetAchieved = currentFPS >= _targets.frameRateStability;

    return ImprovementValidationTest(
      name: 'Frame Rate Stability',
      description: 'Validates frame rate stability improvements',
      baselineValue: _baseline.frameRateAverage,
      currentValue: currentFPS,
      targetValue: _targets.frameRateStability,
      improvementPercentage: improvement,
      targetAchieved: targetAchieved,
      metrics: {
        'averageFPS': currentFPS,
        'jankCount': frameRateTest.metrics['jankCount'],
        'jankRate': frameRateTest.metrics['jankRate'],
      },
    );
  }

  /// Validate ResponsiveSystem cache performance improvement
  Future<ImprovementValidationTest> _validateResponsiveSystemCacheImprovement(
    BuildContext context,
  ) async {
    // Simulate cache performance test since we can't access private methods
    const queryCount = 50;
    var cacheHits = 0;
    var totalQueryTime = 0.0;

    for (int i = 0; i < queryCount; i++) {
      final queryStart = DateTime.now();

      // Make ResponsiveSystem queries (these should be cached)
      ResponsiveSystem.getAdaptiveTouchTargetSize(context);
      ResponsiveSystem.getAdaptiveColumnCount(context);
      ResponsiveSystem.getAdaptiveContentDensity(context);

      final queryEnd = DateTime.now();
      final queryTime = queryEnd.difference(queryStart).inMicroseconds / 1000.0;
      totalQueryTime += queryTime;

      // Assume cache hit if query is very fast (<1ms)
      if (queryTime < 1.0) {
        cacheHits++;
      }
    }

    final currentCacheHitRate = (cacheHits / queryCount) * 100;
    final currentQueryTime = totalQueryTime / queryCount;

    final queryTimeImprovement = _calculateImprovement(
        _baseline.responsiveSystemQueryTime, currentQueryTime);
    final targetAchieved =
        currentCacheHitRate >= _targets.responsiveSystemCacheHitRate;

    return ImprovementValidationTest(
      name: 'ResponsiveSystem Cache Performance',
      description: 'Validates ResponsiveSystem cache performance improvements',
      baselineValue: _baseline.responsiveSystemQueryTime,
      currentValue: currentQueryTime,
      targetValue:
          _baseline.responsiveSystemQueryTime * 0.5, // 50% reduction target
      improvementPercentage: queryTimeImprovement,
      targetAchieved: targetAchieved,
      metrics: {
        'cacheHitRate': currentCacheHitRate,
        'averageQueryTime': currentQueryTime,
        'totalQueries': queryCount,
      },
    );
  }

  /// Calculate improvement percentage
  double _calculateImprovement(double baseline, double current) {
    if (baseline == 0) return 0.0;
    return ((baseline - current) / baseline) * 100;
  }

  /// Calculate overall improvement score
  double _calculateOverallImprovementScore(
      List<ImprovementValidationTest> tests) {
    if (tests.isEmpty) return 0.0;

    // Weight different improvements based on importance
    final weights = {
      'Orientation Change Performance': 0.25,
      'NavigationRail Optimization': 0.20,
      'Memory Efficiency': 0.20,
      'Frame Rate Stability': 0.20,
      'ResponsiveSystem Cache Performance': 0.15,
    };

    double weightedScore = 0.0;
    double totalWeight = 0.0;

    for (final test in tests) {
      final weight = weights[test.name] ?? 0.0;
      weightedScore += test.improvementPercentage * weight;
      totalWeight += weight;
    }

    return totalWeight > 0 ? weightedScore / totalWeight : 0.0;
  }

  /// Get validation history
  List<ValidationResult> getValidationHistory({int? limit}) {
    final results = List<ValidationResult>.from(_validationHistory);
    if (limit != null && results.length > limit) {
      return results.sublist(results.length - limit);
    }
    return results;
  }

  /// Get validation statistics
  ValidationStatistics getValidationStatistics() {
    if (_validationHistory.isEmpty) {
      return const ValidationStatistics(
        totalValidations: 0,
        successfulValidations: 0,
        averageImprovementScore: 0.0,
        targetsAchievedPercentage: 0.0,
      );
    }

    final successfulValidations =
        _validationHistory.where((r) => r.targetsAchieved).length;
    final averageScore = _validationHistory.fold<double>(
            0.0, (sum, r) => sum + r.improvementScore) /
        _validationHistory.length;
    final targetsAchievedPercentage =
        (successfulValidations / _validationHistory.length) * 100;

    return ValidationStatistics(
      totalValidations: _validationHistory.length,
      successfulValidations: successfulValidations,
      averageImprovementScore: averageScore,
      targetsAchievedPercentage: targetsAchievedPercentage,
    );
  }

  /// Clear validation history
  void clearHistory() {
    _validationHistory.clear();
    if (kDebugMode) {
      AnxLog.info('🎯 PerformanceImprovementValidator history cleared');
    }
  }

  /// Dispose validator resources
  void dispose() {
    _isValidating = false;
    _validationHistory.clear();
    if (kDebugMode) {
      AnxLog.info('🎯 PerformanceImprovementValidator disposed');
    }
  }
}

/// Performance improvement targets (15-20% improvement goals)
class PerformanceTargets {
  const PerformanceTargets({
    required this.orientationChangeTime,
    required this.navigationRailOptimization,
    required this.memoryEfficiencyGain,
    required this.overallPerformanceImprovement,
    required this.frameRateStability,
    required this.responsiveSystemCacheHitRate,
  });

  final double orientationChangeTime; // Target: <100ms
  final double navigationRailOptimization; // Target: 15% improvement
  final double memoryEfficiencyGain; // Target: 20% improvement
  final double overallPerformanceImprovement; // Target: 17.5% average
  final double frameRateStability; // Target: 95% stable frames
  final double responsiveSystemCacheHitRate; // Target: 85% cache hit rate
}

/// Baseline performance metrics (pre-optimization values)
class BaselineMetrics {
  const BaselineMetrics({
    required this.orientationChangeTime,
    required this.navigationRailRenderTime,
    required this.memoryUsagePercentage,
    required this.frameRateAverage,
    required this.responsiveSystemQueryTime,
  });

  final double orientationChangeTime; // Baseline: 150ms
  final double navigationRailRenderTime; // Baseline: 20ms
  final double memoryUsagePercentage; // Baseline: 75%
  final double frameRateAverage; // Baseline: 52fps
  final double responsiveSystemQueryTime; // Baseline: 5ms
}

/// Individual improvement validation test
class ImprovementValidationTest {
  const ImprovementValidationTest({
    required this.name,
    required this.description,
    required this.baselineValue,
    required this.currentValue,
    required this.targetValue,
    required this.improvementPercentage,
    required this.targetAchieved,
    required this.metrics,
  });

  final String name;
  final String description;
  final double baselineValue;
  final double currentValue;
  final double targetValue;
  final double improvementPercentage;
  final bool targetAchieved;
  final Map<String, dynamic> metrics;

  @override
  String toString() {
    return 'ImprovementValidationTest($name: ${targetAchieved ? 'ACHIEVED' : 'NOT ACHIEVED'} - ${improvementPercentage.toStringAsFixed(1)}% improvement)';
  }
}

/// Complete validation result
class ValidationResult {
  const ValidationResult({
    required this.deviceTestResult,
    required this.validationTests,
    required this.improvementScore,
    required this.targetsAchieved,
    required this.validationDuration,
    required this.timestamp,
  });

  final DeviceTestResult deviceTestResult;
  final List<ImprovementValidationTest> validationTests;
  final double improvementScore;
  final bool targetsAchieved;
  final Duration validationDuration;
  final DateTime timestamp;

  @override
  String toString() {
    return 'ValidationResult(${targetsAchieved ? 'ACHIEVED' : 'NOT ACHIEVED'}, ${improvementScore.toStringAsFixed(1)}% improvement)';
  }
}

/// Validation statistics summary
class ValidationStatistics {
  const ValidationStatistics({
    required this.totalValidations,
    required this.successfulValidations,
    required this.averageImprovementScore,
    required this.targetsAchievedPercentage,
  });

  final int totalValidations;
  final int successfulValidations;
  final double averageImprovementScore;
  final double targetsAchievedPercentage;

  @override
  String toString() {
    return 'ValidationStatistics($successfulValidations/$totalValidations successful, ${targetsAchievedPercentage.toStringAsFixed(1)}% targets achieved, Avg improvement: ${averageImprovementScore.toStringAsFixed(1)}%)';
  }
}
