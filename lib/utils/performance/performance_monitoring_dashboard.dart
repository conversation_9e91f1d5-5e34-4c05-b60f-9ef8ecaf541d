import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/performance/performance_metrics.dart';
import 'package:dasso_reader/utils/performance/device_testing_framework.dart';
import 'package:dasso_reader/utils/performance/performance_improvement_validator.dart';
import 'package:dasso_reader/utils/performance/orientation_change_monitor.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';

/// Performance monitoring dashboard for tablet-specific insights
/// Provides real-time monitoring and analytics for form factor performance differences
class PerformanceMonitoringDashboard {
  static final PerformanceMonitoringDashboard _instance =
      PerformanceMonitoringDashboard._internal();
  factory PerformanceMonitoringDashboard() => _instance;
  PerformanceMonitoringDashboard._internal();

  // Dashboard state
  bool _isMonitoring = false;
  Timer? _updateTimer;
  final List<DashboardSnapshot> _snapshots = [];

  // Monitoring components
  final PerformanceMetrics _performanceMetrics = PerformanceMetrics();
  final DeviceTestingFramework _deviceTesting = DeviceTestingFramework();
  final PerformanceImprovementValidator _validator =
      PerformanceImprovementValidator();
  final OrientationChangeMonitor _orientationMonitor =
      OrientationChangeMonitor();

  // Dashboard configuration
  static const Duration _updateInterval = Duration(seconds: 30);
  static const int _maxSnapshots =
      200; // Store last 200 snapshots (~100 minutes)

  /// Initialize the performance monitoring dashboard
  Future<void> initialize() async {
    await _performanceMetrics.initialize();
    await _deviceTesting.initialize();
    await _validator.initialize();

    if (kDebugMode) {
      AnxLog.info('📊 PerformanceMonitoringDashboard initialized');
    }
  }

  /// Start real-time performance monitoring
  Future<void> startMonitoring(BuildContext context) async {
    if (_isMonitoring) return;

    _isMonitoring = true;
    _orientationMonitor.initialize(context);
    _performanceMetrics.startMonitoring();

    // Start periodic dashboard updates
    _updateTimer = Timer.periodic(_updateInterval, (_) async {
      await _captureSnapshot(context);
    });

    // Capture initial snapshot
    await _captureSnapshot(context);

    if (kDebugMode) {
      AnxLog.info('📊 Performance monitoring dashboard started');
    }
  }

  /// Stop performance monitoring
  void stopMonitoring() {
    if (!_isMonitoring) return;

    _isMonitoring = false;
    _updateTimer?.cancel();
    _updateTimer = null;
    _performanceMetrics.stopMonitoring();

    if (kDebugMode) {
      AnxLog.info('📊 Performance monitoring dashboard stopped');
    }
  }

  /// Capture performance snapshot
  Future<void> _captureSnapshot(BuildContext context) async {
    try {
      final deviceInfo = _detectDeviceInfo(context);
      final performanceReport =
          _performanceMetrics.getPerformanceReport(context: context);
      final orientationSummary = _orientationMonitor.getPerformanceSummary();

      final snapshot = DashboardSnapshot(
        timestamp: DateTime.now(),
        deviceInfo: deviceInfo,
        performanceReport: performanceReport,
        orientationSummary: orientationSummary,
        formFactorMetrics: _calculateFormFactorMetrics(
            deviceInfo, performanceReport, orientationSummary),
      );

      _snapshots.add(snapshot);
      if (_snapshots.length > _maxSnapshots) {
        _snapshots.removeAt(0);
      }

      if (kDebugMode && _snapshots.length % 10 == 0) {
        AnxLog.info(
            '📊 Dashboard snapshot captured (${_snapshots.length} total)');
      }
    } catch (e) {
      AnxLog.warning('Failed to capture dashboard snapshot: $e');
    }
  }

  /// Detect device information
  DeviceInfo _detectDeviceInfo(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final isTablet = DesignSystem.isTablet(context);
    final orientation = ResponsiveSystem.getOrientation(context);

    return DeviceInfo(
      isTablet: isTablet,
      screenSize: mediaQuery.size,
      orientation: orientation,
      devicePixelRatio: mediaQuery.devicePixelRatio,
      platform: defaultTargetPlatform,
      formFactor: isTablet ? FormFactor.tablet : FormFactor.mobile,
    );
  }

  /// Calculate form factor specific metrics
  FormFactorMetrics _calculateFormFactorMetrics(
    DeviceInfo deviceInfo,
    PerformanceReport performanceReport,
    OrientationPerformanceSummary orientationSummary,
  ) {
    // Calculate tablet-specific metrics
    final tabletScore = deviceInfo.isTablet
        ? _calculateTabletScore(performanceReport, orientationSummary)
        : 0.0;

    // Calculate mobile-specific metrics
    final mobileScore =
        !deviceInfo.isTablet ? _calculateMobileScore(performanceReport) : 0.0;

    // Calculate form factor efficiency
    final formFactorEfficiency =
        _calculateFormFactorEfficiency(deviceInfo, performanceReport);

    return FormFactorMetrics(
      formFactor: deviceInfo.formFactor,
      tabletOptimizationScore: tabletScore,
      mobileOptimizationScore: mobileScore,
      formFactorEfficiency: formFactorEfficiency,
      orientationChangePerformance: orientationSummary.averageTabletChangeTime,
      navigationLayoutOptimization:
          _calculateNavigationOptimization(deviceInfo),
      memoryEfficiencyScore: performanceReport.memoryMetrics.performanceScore,
      frameRateStabilityScore:
          performanceReport.frameRateMetrics.performanceScore,
    );
  }

  /// Calculate tablet optimization score
  double _calculateTabletScore(PerformanceReport report,
      OrientationPerformanceSummary orientationSummary) {
    final orientationScore = orientationSummary.slowTabletChangePercentage < 10
        ? 100.0
        : (100.0 - orientationSummary.slowTabletChangePercentage * 2)
            .clamp(0.0, 100.0);

    final memoryScore = report.memoryMetrics.performanceScore;
    final frameScore = report.frameRateMetrics.performanceScore;

    // Tablet-specific weighting: orientation (40%), memory (30%), frames (30%)
    return (orientationScore * 0.4 + memoryScore * 0.3 + frameScore * 0.3)
        .clamp(0.0, 100.0);
  }

  /// Calculate mobile optimization score
  double _calculateMobileScore(PerformanceReport report) {
    final memoryScore = report.memoryMetrics.performanceScore;
    final frameScore = report.frameRateMetrics.performanceScore;
    final batteryScore = report.batteryMetrics.performanceScore;

    // Mobile-specific weighting: frames (40%), memory (30%), battery (30%)
    return (frameScore * 0.4 + memoryScore * 0.3 + batteryScore * 0.3)
        .clamp(0.0, 100.0);
  }

  /// Calculate form factor efficiency
  double _calculateFormFactorEfficiency(
      DeviceInfo deviceInfo, PerformanceReport report) {
    final baseScore = report.overallScore;

    // Apply form factor specific adjustments
    if (deviceInfo.isTablet) {
      // Tablets should have better memory and orientation performance
      final tabletBonus = report.tabletMetrics?.formFactorScore ?? 0.0;
      return (baseScore + tabletBonus * 0.2).clamp(0.0, 100.0);
    } else {
      // Mobile should have better battery and startup performance
      final batteryBonus =
          report.batteryMetrics.performanceScore > 80 ? 5.0 : 0.0;
      final startupBonus =
          report.startupMetrics.performanceScore > 80 ? 5.0 : 0.0;
      return (baseScore + batteryBonus + startupBonus).clamp(0.0, 100.0);
    }
  }

  /// Calculate navigation layout optimization
  double _calculateNavigationOptimization(DeviceInfo deviceInfo) {
    if (deviceInfo.isTablet &&
        deviceInfo.orientation == Orientation.landscape) {
      // Tablet landscape should use NavigationRail (optimized)
      return 95.0;
    } else if (deviceInfo.isTablet &&
        deviceInfo.orientation == Orientation.portrait) {
      // Tablet portrait should use BottomNavigation (standard)
      return 85.0;
    } else {
      // Mobile always uses BottomNavigation (standard)
      return 90.0;
    }
  }

  /// Get current dashboard data
  DashboardData getCurrentDashboardData() {
    if (_snapshots.isEmpty) {
      return DashboardData.empty();
    }

    final latestSnapshot = _snapshots.last;
    final analytics = _calculateAnalytics();
    final trends = _calculateTrends();

    return DashboardData(
      currentSnapshot: latestSnapshot,
      analytics: analytics,
      trends: trends,
      isMonitoring: _isMonitoring,
      totalSnapshots: _snapshots.length,
      monitoringDuration: _calculateMonitoringDuration(),
    );
  }

  /// Calculate performance analytics
  DashboardAnalytics _calculateAnalytics() {
    if (_snapshots.isEmpty) {
      return DashboardAnalytics.empty();
    }

    final tabletSnapshots =
        _snapshots.where((s) => s.deviceInfo.isTablet).toList();
    final mobileSnapshots =
        _snapshots.where((s) => !s.deviceInfo.isTablet).toList();

    return DashboardAnalytics(
      averageTabletScore: tabletSnapshots.isEmpty
          ? 0.0
          : tabletSnapshots.fold<double>(
                  0.0,
                  (sum, s) =>
                      sum + s.formFactorMetrics.tabletOptimizationScore) /
              tabletSnapshots.length,
      averageMobileScore: mobileSnapshots.isEmpty
          ? 0.0
          : mobileSnapshots.fold<double>(
                  0.0,
                  (sum, s) =>
                      sum + s.formFactorMetrics.mobileOptimizationScore) /
              mobileSnapshots.length,
      tabletVsMobilePerformance:
          _calculateTabletVsMobilePerformance(tabletSnapshots, mobileSnapshots),
      orientationChangeEfficiency: _calculateOrientationChangeEfficiency(),
      memoryEfficiencyTrend: _calculateMemoryEfficiencyTrend(),
      frameRateStabilityTrend: _calculateFrameRateStabilityTrend(),
    );
  }

  /// Calculate performance trends
  DashboardTrends _calculateTrends() {
    if (_snapshots.length < 2) {
      return DashboardTrends.empty();
    }

    final recentSnapshots = _snapshots.length > 20
        ? _snapshots.sublist(_snapshots.length - 20)
        : _snapshots;

    return DashboardTrends(
      performanceScoreTrend: _calculateScoreTrend(recentSnapshots),
      orientationChangeTrend: _calculateOrientationTrend(recentSnapshots),
      memoryUsageTrend: _calculateMemoryTrend(recentSnapshots),
      frameRateTrend: _calculateFrameRateTrend(recentSnapshots),
      formFactorEfficiencyTrend:
          _calculateFormFactorEfficiencyTrend(recentSnapshots),
    );
  }

  /// Calculate tablet vs mobile performance comparison
  double _calculateTabletVsMobilePerformance(
      List<DashboardSnapshot> tabletSnapshots,
      List<DashboardSnapshot> mobileSnapshots) {
    if (tabletSnapshots.isEmpty || mobileSnapshots.isEmpty) return 0.0;

    final avgTabletScore = tabletSnapshots.fold<double>(
            0.0, (sum, s) => sum + s.performanceReport.overallScore) /
        tabletSnapshots.length;
    final avgMobileScore = mobileSnapshots.fold<double>(
            0.0, (sum, s) => sum + s.performanceReport.overallScore) /
        mobileSnapshots.length;

    return avgTabletScore -
        avgMobileScore; // Positive means tablet performs better
  }

  /// Calculate orientation change efficiency
  double _calculateOrientationChangeEfficiency() {
    final tabletSnapshots =
        _snapshots.where((s) => s.deviceInfo.isTablet).toList();
    if (tabletSnapshots.isEmpty) return 0.0;

    final avgChangeTime = tabletSnapshots.fold<double>(0.0,
            (sum, s) => sum + s.orientationSummary.averageTabletChangeTime) /
        tabletSnapshots.length;
    return avgChangeTime < 100
        ? 100.0
        : (200 - avgChangeTime).clamp(0.0, 100.0);
  }

  /// Calculate memory efficiency trend
  TrendDirection _calculateMemoryEfficiencyTrend() {
    if (_snapshots.length < 5) return TrendDirection.stable;

    final recent = _snapshots.sublist(_snapshots.length - 5);
    final scores = recent
        .map((s) => s.performanceReport.memoryMetrics.performanceScore)
        .toList();

    return _calculateTrendDirection(scores);
  }

  /// Calculate frame rate stability trend
  TrendDirection _calculateFrameRateStabilityTrend() {
    if (_snapshots.length < 5) return TrendDirection.stable;

    final recent = _snapshots.sublist(_snapshots.length - 5);
    final scores = recent
        .map((s) => s.performanceReport.frameRateMetrics.performanceScore)
        .toList();

    return _calculateTrendDirection(scores);
  }

  /// Calculate score trend
  TrendDirection _calculateScoreTrend(List<DashboardSnapshot> snapshots) {
    final scores =
        snapshots.map((s) => s.performanceReport.overallScore).toList();
    return _calculateTrendDirection(scores);
  }

  /// Calculate orientation trend
  TrendDirection _calculateOrientationTrend(List<DashboardSnapshot> snapshots) {
    final times = snapshots
        .map((s) => s.orientationSummary.averageTabletChangeTime)
        .toList();
    return _calculateTrendDirection(times,
        inverse: true); // Lower is better for orientation change time
  }

  /// Calculate memory trend
  TrendDirection _calculateMemoryTrend(List<DashboardSnapshot> snapshots) {
    final usage = snapshots
        .map((s) => s.performanceReport.memoryMetrics.currentUsageMB.toDouble())
        .toList();
    return _calculateTrendDirection(usage,
        inverse: true); // Lower is better for memory usage
  }

  /// Calculate frame rate trend
  TrendDirection _calculateFrameRateTrend(List<DashboardSnapshot> snapshots) {
    final frameRates = snapshots
        .map((s) => s.performanceReport.frameRateMetrics.averageFPS)
        .toList();
    return _calculateTrendDirection(frameRates);
  }

  /// Calculate form factor efficiency trend
  TrendDirection _calculateFormFactorEfficiencyTrend(
      List<DashboardSnapshot> snapshots) {
    final efficiency =
        snapshots.map((s) => s.formFactorMetrics.formFactorEfficiency).toList();
    return _calculateTrendDirection(efficiency);
  }

  /// Calculate trend direction from a list of values
  TrendDirection _calculateTrendDirection(List<double> values,
      {bool inverse = false}) {
    if (values.length < 3) return TrendDirection.stable;

    final first =
        values.take(values.length ~/ 2).fold<double>(0.0, (sum, v) => sum + v) /
            (values.length ~/ 2);
    final last =
        values.skip(values.length ~/ 2).fold<double>(0.0, (sum, v) => sum + v) /
            (values.length - values.length ~/ 2);

    final difference = inverse ? first - last : last - first;
    const threshold = 2.0; // 2% threshold for trend detection

    if (difference > threshold) return TrendDirection.improving;
    if (difference < -threshold) return TrendDirection.declining;
    return TrendDirection.stable;
  }

  /// Calculate monitoring duration
  Duration _calculateMonitoringDuration() {
    if (_snapshots.isEmpty) return Duration.zero;
    return DateTime.now().difference(_snapshots.first.timestamp);
  }

  /// Get snapshots for a specific time range
  List<DashboardSnapshot> getSnapshotsInRange(DateTime start, DateTime end) {
    return _snapshots
        .where((s) => s.timestamp.isAfter(start) && s.timestamp.isBefore(end))
        .toList();
  }

  /// Get snapshots for a specific form factor
  List<DashboardSnapshot> getSnapshotsForFormFactor(FormFactor formFactor) {
    return _snapshots
        .where((s) => s.deviceInfo.formFactor == formFactor)
        .toList();
  }

  /// Clear dashboard data
  void clearData() {
    _snapshots.clear();
    if (kDebugMode) {
      AnxLog.info('📊 Performance monitoring dashboard data cleared');
    }
  }

  /// Dispose dashboard resources
  void dispose() {
    stopMonitoring();
    _snapshots.clear();
    if (kDebugMode) {
      AnxLog.info('📊 PerformanceMonitoringDashboard disposed');
    }
  }
}

/// Dashboard snapshot containing performance data at a specific time
class DashboardSnapshot {
  const DashboardSnapshot({
    required this.timestamp,
    required this.deviceInfo,
    required this.performanceReport,
    required this.orientationSummary,
    required this.formFactorMetrics,
  });

  final DateTime timestamp;
  final DeviceInfo deviceInfo;
  final PerformanceReport performanceReport;
  final OrientationPerformanceSummary orientationSummary;
  final FormFactorMetrics formFactorMetrics;
}

/// Form factor specific performance metrics
class FormFactorMetrics {
  const FormFactorMetrics({
    required this.formFactor,
    required this.tabletOptimizationScore,
    required this.mobileOptimizationScore,
    required this.formFactorEfficiency,
    required this.orientationChangePerformance,
    required this.navigationLayoutOptimization,
    required this.memoryEfficiencyScore,
    required this.frameRateStabilityScore,
  });

  final FormFactor formFactor;
  final double tabletOptimizationScore;
  final double mobileOptimizationScore;
  final double formFactorEfficiency;
  final double orientationChangePerformance;
  final double navigationLayoutOptimization;
  final double memoryEfficiencyScore;
  final double frameRateStabilityScore;
}

/// Complete dashboard data
class DashboardData {
  const DashboardData({
    required this.currentSnapshot,
    required this.analytics,
    required this.trends,
    required this.isMonitoring,
    required this.totalSnapshots,
    required this.monitoringDuration,
  });

  final DashboardSnapshot currentSnapshot;
  final DashboardAnalytics analytics;
  final DashboardTrends trends;
  final bool isMonitoring;
  final int totalSnapshots;
  final Duration monitoringDuration;

  factory DashboardData.empty() {
    // Create a simple empty performance report using the PerformanceMetrics system
    final performanceMetrics = PerformanceMetrics();
    final emptyReport = performanceMetrics.getPerformanceReport();

    return DashboardData(
      currentSnapshot: DashboardSnapshot(
        timestamp: DateTime.now(),
        deviceInfo: const DeviceInfo(
          isTablet: false,
          screenSize: Size.zero,
          orientation: Orientation.portrait,
          devicePixelRatio: 1.0,
          platform: TargetPlatform.android,
          formFactor: FormFactor.mobile,
        ),
        performanceReport: emptyReport,
        orientationSummary: const OrientationPerformanceSummary(
          totalChanges: 0,
          tabletChanges: 0,
          mobileChanges: 0,
          averageTabletChangeTime: 0.0,
          averageMobileChangeTime: 0.0,
          slowTabletChangePercentage: 0.0,
          slowMobileChangePercentage: 0.0,
        ),
        formFactorMetrics: const FormFactorMetrics(
          formFactor: FormFactor.mobile,
          tabletOptimizationScore: 0.0,
          mobileOptimizationScore: 0.0,
          formFactorEfficiency: 0.0,
          orientationChangePerformance: 0.0,
          navigationLayoutOptimization: 0.0,
          memoryEfficiencyScore: 0.0,
          frameRateStabilityScore: 0.0,
        ),
      ),
      analytics: DashboardAnalytics.empty(),
      trends: DashboardTrends.empty(),
      isMonitoring: false,
      totalSnapshots: 0,
      monitoringDuration: Duration.zero,
    );
  }
}

/// Dashboard analytics data
class DashboardAnalytics {
  const DashboardAnalytics({
    required this.averageTabletScore,
    required this.averageMobileScore,
    required this.tabletVsMobilePerformance,
    required this.orientationChangeEfficiency,
    required this.memoryEfficiencyTrend,
    required this.frameRateStabilityTrend,
  });

  final double averageTabletScore;
  final double averageMobileScore;
  final double
      tabletVsMobilePerformance; // Positive means tablet performs better
  final double orientationChangeEfficiency;
  final TrendDirection memoryEfficiencyTrend;
  final TrendDirection frameRateStabilityTrend;

  factory DashboardAnalytics.empty() {
    return const DashboardAnalytics(
      averageTabletScore: 0.0,
      averageMobileScore: 0.0,
      tabletVsMobilePerformance: 0.0,
      orientationChangeEfficiency: 0.0,
      memoryEfficiencyTrend: TrendDirection.stable,
      frameRateStabilityTrend: TrendDirection.stable,
    );
  }
}

/// Dashboard trends data
class DashboardTrends {
  const DashboardTrends({
    required this.performanceScoreTrend,
    required this.orientationChangeTrend,
    required this.memoryUsageTrend,
    required this.frameRateTrend,
    required this.formFactorEfficiencyTrend,
  });

  final TrendDirection performanceScoreTrend;
  final TrendDirection orientationChangeTrend;
  final TrendDirection memoryUsageTrend;
  final TrendDirection frameRateTrend;
  final TrendDirection formFactorEfficiencyTrend;

  factory DashboardTrends.empty() {
    return const DashboardTrends(
      performanceScoreTrend: TrendDirection.stable,
      orientationChangeTrend: TrendDirection.stable,
      memoryUsageTrend: TrendDirection.stable,
      frameRateTrend: TrendDirection.stable,
      formFactorEfficiencyTrend: TrendDirection.stable,
    );
  }
}

/// Trend direction enumeration
enum TrendDirection {
  improving,
  stable,
  declining,
}

/// Extension for trend direction display
extension TrendDirectionExtension on TrendDirection {
  String get displayName {
    switch (this) {
      case TrendDirection.improving:
        return 'Improving';
      case TrendDirection.stable:
        return 'Stable';
      case TrendDirection.declining:
        return 'Declining';
    }
  }

  String get emoji {
    switch (this) {
      case TrendDirection.improving:
        return '📈';
      case TrendDirection.stable:
        return '➡️';
      case TrendDirection.declining:
        return '📉';
    }
  }
}
