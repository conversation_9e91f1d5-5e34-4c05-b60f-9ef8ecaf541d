import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';

/// Orientation change performance monitoring for tablet devices
/// Tracks transition times and identifies slow orientation changes (>100ms threshold)
class OrientationChangeMonitor {
  static final OrientationChangeMonitor _instance =
      OrientationChangeMonitor._internal();
  factory OrientationChangeMonitor() => _instance;
  OrientationChangeMonitor._internal();

  // Performance tracking - Apply mobile-proven optimization
  static const int _slowOrientationThresholdMs =
      80; // Reduced from 100ms to 80ms
  static const int _maxHistorySize = 50;

  final List<OrientationChangeMetrics> _changeHistory = [];
  Orientation? _lastOrientation;
  DateTime? _changeStartTime;
  bool _isMonitoring = false;

  // Callbacks for performance issues
  final List<void Function(OrientationChangeMetrics)> _slowChangeCallbacks = [];

  /// Initialize orientation change monitoring
  void initialize(BuildContext context) {
    if (_isMonitoring) return;

    _isMonitoring = true;
    _lastOrientation = ResponsiveSystem.getOrientation(context);

    if (kDebugMode) {
      AnxLog.info('🔄 OrientationChangeMonitor initialized');
    }
  }

  /// Track orientation change start
  void trackOrientationChangeStart(BuildContext context) {
    if (!_isMonitoring) return;

    final currentOrientation = ResponsiveSystem.getOrientation(context);

    // Only track if orientation actually changed
    if (_lastOrientation != null && _lastOrientation != currentOrientation) {
      _changeStartTime = DateTime.now();

      if (kDebugMode) {
        AnxLog.info(
          '🔄 Orientation change started: $_lastOrientation → $currentOrientation',
        );
      }
    }
  }

  /// Track orientation change completion
  void trackOrientationChangeComplete(BuildContext context) {
    if (!_isMonitoring || _changeStartTime == null) return;

    final endTime = DateTime.now();
    final duration = endTime.difference(_changeStartTime!);
    final currentOrientation = ResponsiveSystem.getOrientation(context);
    final isTablet = DesignSystem.isTablet(context);

    final metrics = OrientationChangeMetrics(
      fromOrientation: _lastOrientation!,
      toOrientation: currentOrientation,
      durationMs: duration.inMilliseconds,
      isTablet: isTablet,
      timestamp: endTime,
      isSlow: duration.inMilliseconds > _slowOrientationThresholdMs,
    );

    // Add to history
    _changeHistory.add(metrics);
    if (_changeHistory.length > _maxHistorySize) {
      _changeHistory.removeAt(0);
    }

    // Update tracking state
    _lastOrientation = currentOrientation;
    _changeStartTime = null;

    // Log performance
    if (kDebugMode) {
      final status = metrics.isSlow ? '⚠️ SLOW' : '✅ FAST';
      AnxLog.info(
        '🔄 Orientation change completed: ${duration.inMilliseconds}ms $status',
      );
    }

    // Notify callbacks for slow changes
    if (metrics.isSlow) {
      for (final callback in _slowChangeCallbacks) {
        try {
          callback(metrics);
        } catch (e) {
          AnxLog.warning('OrientationChangeMonitor callback error: $e');
        }
      }
    }
  }

  /// Add callback for slow orientation changes
  void addSlowChangeCallback(void Function(OrientationChangeMetrics) callback) {
    _slowChangeCallbacks.add(callback);
  }

  /// Remove callback for slow orientation changes
  void removeSlowChangeCallback(
    void Function(OrientationChangeMetrics) callback,
  ) {
    _slowChangeCallbacks.remove(callback);
  }

  /// Get recent orientation change metrics
  List<OrientationChangeMetrics> getRecentMetrics({int? limit}) {
    final recentMetrics = List<OrientationChangeMetrics>.from(_changeHistory);
    if (limit != null && recentMetrics.length > limit) {
      return recentMetrics.sublist(recentMetrics.length - limit);
    }
    return recentMetrics;
  }

  /// Get average orientation change time
  double getAverageChangeTime({bool tabletOnly = false}) {
    final relevantMetrics = tabletOnly
        ? _changeHistory.where((m) => m.isTablet).toList()
        : _changeHistory;

    if (relevantMetrics.isEmpty) return 0.0;

    final totalMs =
        relevantMetrics.fold<int>(0, (sum, m) => sum + m.durationMs);
    return totalMs / relevantMetrics.length;
  }

  /// Get percentage of slow orientation changes
  double getSlowChangePercentage({bool tabletOnly = false}) {
    final relevantMetrics = tabletOnly
        ? _changeHistory.where((m) => m.isTablet).toList()
        : _changeHistory;

    if (relevantMetrics.isEmpty) return 0.0;

    final slowCount = relevantMetrics.where((m) => m.isSlow).length;
    return (slowCount / relevantMetrics.length) * 100;
  }

  /// Get performance summary
  OrientationPerformanceSummary getPerformanceSummary() {
    final tabletMetrics = _changeHistory.where((m) => m.isTablet).toList();
    final mobileMetrics = _changeHistory.where((m) => !m.isTablet).toList();

    return OrientationPerformanceSummary(
      totalChanges: _changeHistory.length,
      tabletChanges: tabletMetrics.length,
      mobileChanges: mobileMetrics.length,
      averageTabletChangeTime: tabletMetrics.isEmpty
          ? 0.0
          : tabletMetrics.fold<int>(0, (sum, m) => sum + m.durationMs) /
              tabletMetrics.length,
      averageMobileChangeTime: mobileMetrics.isEmpty
          ? 0.0
          : mobileMetrics.fold<int>(0, (sum, m) => sum + m.durationMs) /
              mobileMetrics.length,
      slowTabletChangePercentage: tabletMetrics.isEmpty
          ? 0.0
          : (tabletMetrics.where((m) => m.isSlow).length /
                  tabletMetrics.length) *
              100,
      slowMobileChangePercentage: mobileMetrics.isEmpty
          ? 0.0
          : (mobileMetrics.where((m) => m.isSlow).length /
                  mobileMetrics.length) *
              100,
    );
  }

  /// Clear monitoring history
  void clearHistory() {
    _changeHistory.clear();
    if (kDebugMode) {
      AnxLog.info('🔄 OrientationChangeMonitor history cleared');
    }
  }

  /// Dispose monitoring resources
  void dispose() {
    _isMonitoring = false;
    _slowChangeCallbacks.clear();
    _changeHistory.clear();
    _lastOrientation = null;
    _changeStartTime = null;

    if (kDebugMode) {
      AnxLog.info('🔄 OrientationChangeMonitor disposed');
    }
  }
}

/// Metrics for a single orientation change
class OrientationChangeMetrics {
  final Orientation fromOrientation;
  final Orientation toOrientation;
  final int durationMs;
  final bool isTablet;
  final DateTime timestamp;
  final bool isSlow;

  const OrientationChangeMetrics({
    required this.fromOrientation,
    required this.toOrientation,
    required this.durationMs,
    required this.isTablet,
    required this.timestamp,
    required this.isSlow,
  });

  @override
  String toString() {
    return 'OrientationChangeMetrics(${fromOrientation.name} → ${toOrientation.name}, '
        '${durationMs}ms, ${isTablet ? 'tablet' : 'mobile'}, ${isSlow ? 'slow' : 'fast'})';
  }
}

/// Performance summary for orientation changes
class OrientationPerformanceSummary {
  final int totalChanges;
  final int tabletChanges;
  final int mobileChanges;
  final double averageTabletChangeTime;
  final double averageMobileChangeTime;
  final double slowTabletChangePercentage;
  final double slowMobileChangePercentage;

  const OrientationPerformanceSummary({
    required this.totalChanges,
    required this.tabletChanges,
    required this.mobileChanges,
    required this.averageTabletChangeTime,
    required this.averageMobileChangeTime,
    required this.slowTabletChangePercentage,
    required this.slowMobileChangePercentage,
  });

  @override
  String toString() {
    return 'OrientationPerformanceSummary('
        'total: $totalChanges, '
        'tablet: $tabletChanges (avg: ${averageTabletChangeTime.toStringAsFixed(1)}ms, '
        'slow: ${slowTabletChangePercentage.toStringAsFixed(1)}%), '
        'mobile: $mobileChanges (avg: ${averageMobileChangeTime.toStringAsFixed(1)}ms, '
        'slow: ${slowMobileChangePercentage.toStringAsFixed(1)}%))';
  }
}
