import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/performance/performance_metrics.dart';
import 'package:dasso_reader/utils/performance/orientation_change_monitor.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';

/// Tablet performance monitor for production use
/// Provides real-time monitoring of tablet-specific performance optimizations
/// including orientation changes, NavigationRail efficiency, and form factor detection
class TabletPerformanceMonitor {
  static final TabletPerformanceMonitor _instance =
      TabletPerformanceMonitor._internal();
  factory TabletPerformanceMonitor() => _instance;
  TabletPerformanceMonitor._internal();

  // Monitoring state
  bool _isMonitoring = false;
  final PerformanceMetrics _performanceMetrics = PerformanceMetrics();
  final OrientationChangeMonitor _orientationMonitor =
      OrientationChangeMonitor();

  /// Initialize the performance monitor
  Future<void> initialize() async {
    await _performanceMetrics.initialize();

    if (kDebugMode) {
      AnxLog.info('📊 TabletPerformanceMonitor initialized');
    }
  }

  /// Start performance monitoring
  Future<void> startMonitoring(BuildContext context) async {
    if (_isMonitoring) return;

    _isMonitoring = true;
    _orientationMonitor.initialize(context);
    _performanceMetrics.startMonitoring();

    if (kDebugMode) {
      AnxLog.info('📊 Tablet performance monitoring started');
    }
  }

  /// Stop performance monitoring
  void stopMonitoring() {
    if (!_isMonitoring) return;

    _isMonitoring = false;
    _performanceMetrics.stopMonitoring();

    if (kDebugMode) {
      AnxLog.info('📊 Tablet performance monitoring stopped');
    }
  }

  /// Get current performance data
  PerformanceData getCurrentPerformanceData(BuildContext context) {
    final isTablet = DesignSystem.isTablet(context);
    final orientation = ResponsiveSystem.getOrientation(context);
    final performanceReport =
        _performanceMetrics.getPerformanceReport(context: context);
    final orientationSummary = _orientationMonitor.getPerformanceSummary();

    return PerformanceData(
      isTablet: isTablet,
      orientation: orientation,
      performanceScore: performanceReport.overallScore,
      orientationChangeTime: orientationSummary.averageTabletChangeTime,
      memoryUsage: performanceReport.memoryMetrics.currentUsageMB.toDouble(),
      frameRate: performanceReport.frameRateMetrics.averageFPS,
      isMonitoring: _isMonitoring,
      hasTabletOptimizations: performanceReport.tabletMetrics != null,
    );
  }

  /// Log current performance status
  void logPerformanceStatus(BuildContext context) {
    if (!kDebugMode) return;

    final data = getCurrentPerformanceData(context);
    AnxLog.info('📊 Tablet Performance Status:');
    AnxLog.info('   Device: ${data.isTablet ? 'Tablet' : 'Mobile'}');
    AnxLog.info('   Orientation: ${data.orientation.name}');
    AnxLog.info(
        '   Performance Score: ${data.performanceScore.toStringAsFixed(1)}/100');
    AnxLog.info('   Frame Rate: ${data.frameRate.toStringAsFixed(1)} FPS');
    AnxLog.info('   Memory Usage: ${data.memoryUsage.toStringAsFixed(1)} MB');

    if (data.isTablet) {
      AnxLog.info(
          '   Orientation Change Time: ${data.orientationChangeTime.toStringAsFixed(1)}ms');
      AnxLog.info(
          '   Tablet Optimizations: ${data.hasTabletOptimizations ? 'Active' : 'Inactive'}');
    }
  }

  /// Dispose monitor resources
  void dispose() {
    stopMonitoring();
    if (kDebugMode) {
      AnxLog.info('📊 TabletPerformanceMonitor disposed');
    }
  }
}

/// Simple performance data container
class PerformanceData {
  const PerformanceData({
    required this.isTablet,
    required this.orientation,
    required this.performanceScore,
    required this.orientationChangeTime,
    required this.memoryUsage,
    required this.frameRate,
    required this.isMonitoring,
    required this.hasTabletOptimizations,
  });

  final bool isTablet;
  final Orientation orientation;
  final double performanceScore;
  final double orientationChangeTime;
  final double memoryUsage;
  final double frameRate;
  final bool isMonitoring;
  final bool hasTabletOptimizations;

  @override
  String toString() {
    return 'PerformanceData(${isTablet ? 'Tablet' : 'Mobile'}, Score: ${performanceScore.toStringAsFixed(1)}, FPS: ${frameRate.toStringAsFixed(1)})';
  }
}
