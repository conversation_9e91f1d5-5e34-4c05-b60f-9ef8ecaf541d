import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/performance/performance_metrics.dart';
import 'package:dasso_reader/utils/performance/orientation_change_monitor.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';

/// Comprehensive device testing framework for performance validation
/// Provides systematic testing across Android/iOS mobile and tablet devices
class DeviceTestingFramework {
  static final DeviceTestingFramework _instance =
      DeviceTestingFramework._internal();
  factory DeviceTestingFramework() => _instance;
  DeviceTestingFramework._internal();

  // Testing state
  bool _isRunning = false;
  final List<DeviceTestResult> _testResults = [];
  final PerformanceMetrics _performanceMetrics = PerformanceMetrics();

  // Test configuration
  static const Duration _testDuration = Duration(minutes: 2);
  static const int _maxTestResults = 100;

  /// Initialize the testing framework
  Future<void> initialize() async {
    if (!_isRunning) {
      await _performanceMetrics.initialize();
      if (kDebugMode) {
        AnxLog.info('🧪 DeviceTestingFramework initialized');
      }
    }
  }

  /// Run comprehensive device performance test
  Future<DeviceTestResult> runDevicePerformanceTest(
    BuildContext context,
  ) async {
    if (_isRunning) {
      throw StateError('Device test is already running');
    }

    _isRunning = true;
    final startTime = DateTime.now();

    try {
      if (kDebugMode) {
        AnxLog.info('🧪 Starting device performance test...');
      }

      // Detect device characteristics
      final deviceInfo = _detectDeviceInfo(context);

      // Start performance monitoring
      _performanceMetrics.startMonitoring();

      // Run test suite
      final testSuite = await _runTestSuite(context, deviceInfo);

      // Stop monitoring and collect results
      _performanceMetrics.stopMonitoring();
      final performanceReport =
          _performanceMetrics.getPerformanceReport(context: context);

      final endTime = DateTime.now();
      final testDuration = endTime.difference(startTime);

      final result = DeviceTestResult(
        deviceInfo: deviceInfo,
        performanceReport: performanceReport,
        testSuite: testSuite,
        testDuration: testDuration,
        timestamp: endTime,
        passed: _evaluateTestResult(testSuite, performanceReport),
      );

      // Store result
      _testResults.add(result);
      if (_testResults.length > _maxTestResults) {
        _testResults.removeAt(0);
      }

      if (kDebugMode) {
        AnxLog.info(
            '🧪 Device performance test completed: ${result.passed ? 'PASSED' : 'FAILED'}');
      }

      return result;
    } finally {
      _isRunning = false;
    }
  }

  /// Detect device information
  DeviceInfo _detectDeviceInfo(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final isTablet = DesignSystem.isTablet(context);
    final orientation = ResponsiveSystem.getOrientation(context);

    return DeviceInfo(
      isTablet: isTablet,
      screenSize: mediaQuery.size,
      orientation: orientation,
      devicePixelRatio: mediaQuery.devicePixelRatio,
      platform: defaultTargetPlatform,
      formFactor: isTablet ? FormFactor.tablet : FormFactor.mobile,
    );
  }

  /// Run comprehensive test suite
  Future<TestSuite> _runTestSuite(
      BuildContext context, DeviceInfo deviceInfo) async {
    final tests = <PerformanceTest>[];

    // Test 1: Orientation Change Performance (tablets only)
    if (deviceInfo.isTablet) {
      tests.add(await _testOrientationChangePerformance(context));
    }

    // Test 2: Navigation Layout Performance
    tests.add(await _testNavigationLayoutPerformance(context, deviceInfo));

    // Test 3: ResponsiveSystem Cache Performance
    tests.add(await _testResponsiveSystemCachePerformance(context));

    // Test 4: Memory Efficiency Test
    tests.add(await _testMemoryEfficiency(context));

    // Test 5: Frame Rate Stability Test
    tests.add(await _testFrameRateStability(context));

    return TestSuite(
      tests: tests,
      totalTests: tests.length,
      passedTests: tests.where((t) => t.passed).length,
      failedTests: tests.where((t) => !t.passed).length,
    );
  }

  /// Test orientation change performance (tablets only)
  Future<PerformanceTest> _testOrientationChangePerformance(
      BuildContext context) async {
    final orientationMonitor = OrientationChangeMonitor();
    orientationMonitor.initialize(context);

    // Simulate orientation changes and measure performance
    final startTime = DateTime.now();

    // Get current metrics
    final summary = orientationMonitor.getPerformanceSummary();

    final endTime = DateTime.now();
    final testDuration = endTime.difference(startTime);

    // Evaluate performance (target: <100ms average, <10% slow changes)
    final averageTime = summary.averageTabletChangeTime;
    final slowPercentage = summary.slowTabletChangePercentage;

    final passed = averageTime < 100.0 && slowPercentage < 10.0;

    return PerformanceTest(
      name: 'Orientation Change Performance',
      description: 'Tests tablet orientation change performance',
      duration: testDuration,
      passed: passed,
      metrics: {
        'averageChangeTime': averageTime,
        'slowChangePercentage': slowPercentage,
        'totalChanges': summary.totalChanges,
      },
      target: 'Average <100ms, Slow changes <10%',
      actual:
          'Average ${averageTime.toStringAsFixed(1)}ms, Slow ${slowPercentage.toStringAsFixed(1)}%',
    );
  }

  /// Test navigation layout performance
  Future<PerformanceTest> _testNavigationLayoutPerformance(
      BuildContext context, DeviceInfo deviceInfo) async {
    final startTime = DateTime.now();

    // Test navigation layout switching performance
    final navigationLayoutType =
        deviceInfo.isTablet && deviceInfo.orientation == Orientation.landscape
            ? NavigationLayoutType.navigationRail
            : NavigationLayoutType.bottomNavigation;

    // Simulate layout performance measurement
    final renderTime = await _measureNavigationRenderTime(navigationLayoutType);

    final endTime = DateTime.now();
    final testDuration = endTime.difference(startTime);

    // Evaluate performance (target: <16ms for 60fps)
    final passed = renderTime < 16.0;

    return PerformanceTest(
      name: 'Navigation Layout Performance',
      description: 'Tests navigation layout rendering performance',
      duration: testDuration,
      passed: passed,
      metrics: {
        'renderTime': renderTime,
        'layoutType': navigationLayoutType.toString(),
        'targetFPS': 60.0,
      },
      target: 'Render time <16ms (60fps)',
      actual: 'Render time ${renderTime.toStringAsFixed(1)}ms',
    );
  }

  /// Test ResponsiveSystem cache performance
  Future<PerformanceTest> _testResponsiveSystemCachePerformance(
      BuildContext context) async {
    final startTime = DateTime.now();

    // Test cache performance by making multiple queries
    final queryCount = 100;
    var cacheHits = 0;
    var totalQueryTime = 0.0;

    for (int i = 0; i < queryCount; i++) {
      final queryStart = DateTime.now();

      // Make ResponsiveSystem queries
      ResponsiveSystem.getAdaptiveTouchTargetSize(context);
      ResponsiveSystem.getAdaptiveColumnCount(context);
      ResponsiveSystem.getAdaptiveContentDensity(context);

      final queryEnd = DateTime.now();
      final queryTime = queryEnd.difference(queryStart).inMicroseconds / 1000.0;
      totalQueryTime += queryTime;

      // Assume cache hit if query is very fast (<1ms)
      if (queryTime < 1.0) {
        cacheHits++;
      }
    }

    final endTime = DateTime.now();
    final testDuration = endTime.difference(startTime);

    final cacheHitRate = (cacheHits / queryCount) * 100;
    final averageQueryTime = totalQueryTime / queryCount;

    // Evaluate performance (target: >80% cache hit rate, <2ms average query time)
    final passed = cacheHitRate > 80.0 && averageQueryTime < 2.0;

    return PerformanceTest(
      name: 'ResponsiveSystem Cache Performance',
      description: 'Tests ResponsiveSystem cache efficiency',
      duration: testDuration,
      passed: passed,
      metrics: {
        'cacheHitRate': cacheHitRate,
        'averageQueryTime': averageQueryTime,
        'totalQueries': queryCount,
      },
      target: 'Cache hit rate >80%, Query time <2ms',
      actual:
          'Cache hit rate ${cacheHitRate.toStringAsFixed(1)}%, Query time ${averageQueryTime.toStringAsFixed(2)}ms',
    );
  }

  /// Test memory efficiency
  Future<PerformanceTest> _testMemoryEfficiency(BuildContext context) async {
    final startTime = DateTime.now();

    // Get memory metrics
    final memoryMetrics = _performanceMetrics.memoryMonitor.getMetrics();

    final endTime = DateTime.now();
    final testDuration = endTime.difference(startTime);

    // Evaluate memory efficiency (target: <80% of device limit, no memory pressure)
    final memoryUsagePercentage =
        (memoryMetrics.currentUsageMB / memoryMetrics.deviceLimitMB) * 100;
    final passed =
        memoryUsagePercentage < 80.0 && !memoryMetrics.isMemoryPressure;

    return PerformanceTest(
      name: 'Memory Efficiency',
      description: 'Tests memory usage efficiency',
      duration: testDuration,
      passed: passed,
      metrics: {
        'currentUsageMB': memoryMetrics.currentUsageMB,
        'deviceLimitMB': memoryMetrics.deviceLimitMB,
        'usagePercentage': memoryUsagePercentage,
        'isMemoryPressure': memoryMetrics.isMemoryPressure,
      },
      target: 'Memory usage <80%, No memory pressure',
      actual:
          'Memory usage ${memoryUsagePercentage.toStringAsFixed(1)}%, Pressure: ${memoryMetrics.isMemoryPressure}',
    );
  }

  /// Test frame rate stability
  Future<PerformanceTest> _testFrameRateStability(BuildContext context) async {
    final startTime = DateTime.now();

    // Get frame rate metrics
    final frameMetrics = _performanceMetrics.frameRateMonitor.getMetrics();

    final endTime = DateTime.now();
    final testDuration = endTime.difference(startTime);

    // Evaluate frame rate stability (target: >55fps average, <5% jank rate)
    final jankRate = frameMetrics.totalFrames > 0
        ? (frameMetrics.jankCount / frameMetrics.totalFrames) * 100
        : 0.0;

    final passed = frameMetrics.averageFPS > 55.0 && jankRate < 5.0;

    return PerformanceTest(
      name: 'Frame Rate Stability',
      description: 'Tests frame rate stability and jank detection',
      duration: testDuration,
      passed: passed,
      metrics: {
        'averageFPS': frameMetrics.averageFPS,
        'jankCount': frameMetrics.jankCount,
        'totalFrames': frameMetrics.totalFrames,
        'jankRate': jankRate,
      },
      target: 'Average FPS >55, Jank rate <5%',
      actual:
          'Average FPS ${frameMetrics.averageFPS.toStringAsFixed(1)}, Jank rate ${jankRate.toStringAsFixed(1)}%',
    );
  }

  /// Measure navigation render time (simulated)
  Future<double> _measureNavigationRenderTime(
      NavigationLayoutType layoutType) async {
    // Simulate render time measurement
    await Future<void>.delayed(const Duration(milliseconds: 10));

    // Return simulated render time based on layout type
    switch (layoutType) {
      case NavigationLayoutType.navigationRail:
        return 12.0; // NavigationRail is typically faster
      case NavigationLayoutType.bottomNavigation:
        return 14.0; // BottomNavigation is slightly slower
      case NavigationLayoutType.masterDetail:
        return 15.0; // Master-detail is most complex
    }
  }

  /// Evaluate overall test result
  bool _evaluateTestResult(
      TestSuite testSuite, PerformanceReport performanceReport) {
    // Test passes if:
    // 1. All individual tests pass
    // 2. Overall performance score is >70
    return testSuite.passedTests == testSuite.totalTests &&
        performanceReport.overallScore > 70.0;
  }

  /// Get recent test results
  List<DeviceTestResult> getRecentResults({int? limit}) {
    final results = List<DeviceTestResult>.from(_testResults);
    if (limit != null && results.length > limit) {
      return results.sublist(results.length - limit);
    }
    return results;
  }

  /// Get test statistics
  TestStatistics getTestStatistics() {
    if (_testResults.isEmpty) {
      return const TestStatistics(
        totalTests: 0,
        passedTests: 0,
        failedTests: 0,
        averageScore: 0.0,
        tabletTests: 0,
        mobileTests: 0,
      );
    }

    final passedTests = _testResults.where((r) => r.passed).length;
    final tabletTests = _testResults.where((r) => r.deviceInfo.isTablet).length;
    final averageScore = _testResults.fold<double>(
            0.0, (sum, r) => sum + r.performanceReport.overallScore) /
        _testResults.length;

    return TestStatistics(
      totalTests: _testResults.length,
      passedTests: passedTests,
      failedTests: _testResults.length - passedTests,
      averageScore: averageScore,
      tabletTests: tabletTests,
      mobileTests: _testResults.length - tabletTests,
    );
  }

  /// Clear test results
  void clearResults() {
    _testResults.clear();
    if (kDebugMode) {
      AnxLog.info('🧪 DeviceTestingFramework results cleared');
    }
  }

  /// Dispose framework resources
  void dispose() {
    _isRunning = false;
    _testResults.clear();
    if (kDebugMode) {
      AnxLog.info('🧪 DeviceTestingFramework disposed');
    }
  }
}

/// Device information for testing
class DeviceInfo {
  const DeviceInfo({
    required this.isTablet,
    required this.screenSize,
    required this.orientation,
    required this.devicePixelRatio,
    required this.platform,
    required this.formFactor,
  });

  final bool isTablet;
  final Size screenSize;
  final Orientation orientation;
  final double devicePixelRatio;
  final TargetPlatform platform;
  final FormFactor formFactor;

  @override
  String toString() {
    return 'DeviceInfo(${formFactor.name}, ${platform.name}, ${screenSize.width.toInt()}x${screenSize.height.toInt()}, ${orientation.name})';
  }
}

/// Form factor enumeration
enum FormFactor {
  mobile,
  tablet,
}

/// Individual performance test result
class PerformanceTest {
  const PerformanceTest({
    required this.name,
    required this.description,
    required this.duration,
    required this.passed,
    required this.metrics,
    required this.target,
    required this.actual,
  });

  final String name;
  final String description;
  final Duration duration;
  final bool passed;
  final Map<String, dynamic> metrics;
  final String target;
  final String actual;

  @override
  String toString() {
    return 'PerformanceTest($name: ${passed ? 'PASSED' : 'FAILED'} - $actual)';
  }
}

/// Test suite containing multiple performance tests
class TestSuite {
  const TestSuite({
    required this.tests,
    required this.totalTests,
    required this.passedTests,
    required this.failedTests,
  });

  final List<PerformanceTest> tests;
  final int totalTests;
  final int passedTests;
  final int failedTests;

  double get passRate =>
      totalTests > 0 ? (passedTests / totalTests) * 100 : 0.0;

  @override
  String toString() {
    return 'TestSuite($passedTests/$totalTests passed, ${passRate.toStringAsFixed(1)}% pass rate)';
  }
}

/// Complete device test result
class DeviceTestResult {
  const DeviceTestResult({
    required this.deviceInfo,
    required this.performanceReport,
    required this.testSuite,
    required this.testDuration,
    required this.timestamp,
    required this.passed,
  });

  final DeviceInfo deviceInfo;
  final PerformanceReport performanceReport;
  final TestSuite testSuite;
  final Duration testDuration;
  final DateTime timestamp;
  final bool passed;

  @override
  String toString() {
    return 'DeviceTestResult(${deviceInfo.formFactor.name}, ${passed ? 'PASSED' : 'FAILED'}, Score: ${performanceReport.overallScore.toStringAsFixed(1)})';
  }
}

/// Test statistics summary
class TestStatistics {
  const TestStatistics({
    required this.totalTests,
    required this.passedTests,
    required this.failedTests,
    required this.averageScore,
    required this.tabletTests,
    required this.mobileTests,
  });

  final int totalTests;
  final int passedTests;
  final int failedTests;
  final double averageScore;
  final int tabletTests;
  final int mobileTests;

  double get passRate =>
      totalTests > 0 ? (passedTests / totalTests) * 100 : 0.0;

  @override
  String toString() {
    return 'TestStatistics($passedTests/$totalTests passed, ${passRate.toStringAsFixed(1)}% pass rate, Avg score: ${averageScore.toStringAsFixed(1)})';
  }
}
