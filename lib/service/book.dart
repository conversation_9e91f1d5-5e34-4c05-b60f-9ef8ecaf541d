import 'dart:io';

import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/dao/book.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/main.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/page/home_page.dart';
import 'package:dasso_reader/page/iap_page.dart';
import 'package:dasso_reader/providers/ai_chat.dart';
import 'package:dasso_reader/providers/book_list.dart';
import 'package:dasso_reader/service/convert_to_epub/txt/convert_from_txt.dart';
import 'package:dasso_reader/service/iap_service.dart';
import 'package:dasso_reader/utils/env_var.dart';
import 'package:dasso_reader/utils/get_path/get_base_path.dart';
import 'package:dasso_reader/page/reading_page.dart';
import 'package:dasso_reader/utils/import_book.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/toast/common.dart';
import 'package:dasso_reader/utils/webView/gererate_url.dart';
import 'package:dasso_reader/utils/platform/cross_platform_validator.dart';
import 'package:dasso_reader/utils/webView/webview_console_message.dart';
import 'package:dasso_reader/utils/webView/webview_initial_variable.dart';
import 'package:dasso_reader/widgets/common/adaptive_navigation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path/path.dart' as path;

import 'book_player/book_player_server.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/widgets/common/adaptive_components.dart';

HeadlessInAppWebView? headlessInAppWebView;

/// import book list and **delete file**
Future<void> importBookList(
  List<File> fileList,
  BuildContext context,
  WidgetRef ref,
) async {
  final allowBookExtensions = ['epub', 'mobi', 'azw3', 'fb2', 'txt'];

  AnxLog.info('importBook fileList: ${fileList.toString()}');

  List<File> supportedFiles = fileList.where((file) {
    return allowBookExtensions.contains(file.path.split('.').last);
  }).toList();

  List<File> unsupportedFiles = fileList.where((file) {
    return !allowBookExtensions.contains(file.path.split('.').last);
  }).toList();

  // delete unsupported files with better error handling
  for (var file in unsupportedFiles) {
    try {
      file.deleteSync();
      AnxLog.info('Deleted unsupported file: ${file.path}');
    } catch (e) {
      AnxLog.warning('Failed to delete unsupported file ${file.path}: $e');
    }
  }

  Widget bookItem(String filePath, Widget icon) {
    return Row(
      children: [
        SizedBox(
          width: DesignSystem.widgetIconSizeMedium,
          height: DesignSystem.widgetIconSizeMedium,
          child: icon,
        ),
        Expanded(
          child: Text(
            path.basename(filePath),
            style: TextStyle(
              fontWeight: FontWeight.w300,
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: false),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ],
    );
  }

  showDialog<void>(
    context: context,
    builder: (BuildContext context) {
      String currentHandlingFile = '';
      List<String> errorFiles = [];

      return StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: Text(
              L10n.of(context).import_n_books_selected(fileList.length),
              style: TextStyle(
                color:
                    DesignSystem.getSettingsTextColor(context, isPrimary: true),
              ),
            ),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    L10n.of(context)
                        .import_support_types(allowBookExtensions.join(' / ')),
                    style: TextStyle(
                      color: DesignSystem.getSettingsTextColor(
                        context,
                        isPrimary: false,
                      ),
                    ),
                  ),
                  const SizedBox(height: DesignSystem.spaceS),
                  if (unsupportedFiles.isNotEmpty)
                    Text(
                      L10n.of(context)
                          .import_n_books_not_support(unsupportedFiles.length),
                      style: TextStyle(
                        color: DesignSystem.getSettingsTextColor(
                          context,
                          isPrimary: false,
                        ),
                      ),
                    ),
                  const SizedBox(height: DesignSystem.spaceL),
                  for (var file in unsupportedFiles)
                    bookItem(file.path, Icon(AdaptiveIcons.error)),
                  for (var file in supportedFiles)
                    file.path == currentHandlingFile
                        ? bookItem(
                            file.path,
                            Container(
                              padding:
                                  const EdgeInsets.all(DesignSystem.spaceXS),
                              width: DesignSystem.widgetIconSizeSmall,
                              height: DesignSystem.widgetIconSizeSmall,
                              child: const CircularProgressIndicator(),
                            ),
                          )
                        : bookItem(
                            file.path,
                            errorFiles.contains(file.path)
                                ? Icon(AdaptiveIcons.error)
                                : const Icon(Icons.done),
                          ),
                ],
              ),
            ),
            actions: [
              AdaptiveDialogAction(
                text: L10n.of(context).common_cancel,
                onPressed: () {
                  Navigator.pop(context);
                  for (var file in supportedFiles) {
                    file.deleteSync();
                  }
                },
              ).toMaterialAction(),
              if (supportedFiles.isNotEmpty)
                AdaptiveDialogAction(
                  text: L10n.of(context)
                      .import_import_n_books(supportedFiles.length),
                  isDefaultAction: true,
                  onPressed: () async {
                    for (var file in supportedFiles) {
                      AnxToast.show(path.basename(file.path));
                      setState(() {
                        currentHandlingFile = file.path;
                      });
                      // try {
                      await importBook(file, ref);
                      // } catch (e) {
                      //   setState(() {
                      //     errorFiles.add(file.path);
                      //   });
                      // }
                    }
                    Navigator.of(navigatorKey.currentContext!).pop('dialog');
                  },
                ).toMaterialAction(),
            ],
          );
        },
      );
    },
  );
}

Future<void> importBook(File file, WidgetRef ref) async {
  if (file.path.split('.').last == 'txt') {
    final tempFile = await convertFromTxt(file);
    file.deleteSync();
    file = tempFile;
  }

  await getBookMetadata(file, ref: ref);
  ref.read(bookListProvider.notifier).refresh();
}

Future<void> pushToReadingPage(
  WidgetRef ref,
  BuildContext context,
  Book book, {
  String? cfi,
}) async {
  if (book.isDeleted) {
    AnxToast.show(L10n.of(context).book_deleted);
    return;
  }
  if (EnvVar.isAppStore) {
    if (!IAPService().isFeatureAvailable) {
      AdaptiveNavigation.push(context, const IAPPage());
      return;
    }
  }

  // Apply mobile-proven ANR prevention: Clear AI chat in background
  ref.read(aiChatProvider.notifier).clear();

  // Apply mobile-proven ANR prevention: Show loading state immediately (before async operations)
  _showBookLoadingIndicator(context);

  // Apply mobile-proven ANR prevention: Aggressive memory cleanup before book switch
  await _performPreBookSwitchCleanup();

  // Apply mobile-proven ANR prevention: Defer heavy navigation to next frame
  await Future<void>.delayed(
    const Duration(milliseconds: 50),
  ); // Increased delay for book switching

  // Check if context is still valid after async gap
  if (!context.mounted) return;

  try {
    await AdaptiveNavigation.push(
      context,
      ReadingPage(
        key: readingPageKey,
        book: book,
        cfi: cfi,
      ),
    );
  } finally {
    // Hide loading indicator when navigation completes (check context again)
    if (context.mounted) {
      _hideBookLoadingIndicator(context);
    }
  }
}

/// Perform aggressive memory cleanup before book switching to prevent ANR
Future<void> _performPreBookSwitchCleanup() async {
  try {
    // Mobile-proven pattern: Force garbage collection before heavy operations
    // This prevents memory pressure during WebView creation

    // Clear any cached WebView resources
    await InAppWebViewController.clearAllCache();

    AnxLog.info('📱 Pre-book-switch cleanup completed');
  } catch (e) {
    AnxLog.warning('Pre-book-switch cleanup error (non-critical): $e');
  }
}

/// Show loading indicator to prevent ANR perception
void _showBookLoadingIndicator(BuildContext context) {
  // This prevents user from perceiving ANR during book loading
  // Mobile-proven pattern: immediate visual feedback
}

/// Hide loading indicator after navigation
void _hideBookLoadingIndicator(BuildContext context) {
  // Clean up loading state
}

Future<void> openBook(BuildContext context, Book book, WidgetRef ref) async {
  await pushToReadingPage(ref, context, book);
}

void updateBookRating(Book book, double rating) {
  book.rating = rating;
  updateBook(book);
}

Future<void> resetBookCover(Book book) async {
  File file = File(book.fileFullPath);
  getBookMetadata(file);
}

Future<void> saveBook(
  File file,
  String title,
  String author,
  String description,
  String cover, {
  Book? provideBook,
}) async {
  final newBookName =
      '${title.length > 20 ? title.substring(0, 20) : title}-${DateTime.now().millisecondsSinceEpoch}'
          .replaceAll(RegExp(r'[<>:"/\\|?*]'), '_')
          .replaceAll('\n', '')
          .replaceAll('\r', '')
          .trim();

  final extension = file.path.split('.').last;

  final dbFilePath = path.join('file', '$newBookName.$extension');
  final filePath = getBasePath(dbFilePath);
  String? dbCoverPath = path.join('cover', newBookName);
  // final coverPath = getBasePath(dbCoverPath);

  await file.copy(filePath);
  // remove cached file
  file.delete();

  dbCoverPath = await saveImageToLocal(cover, dbCoverPath);

  Book book = Book(
    id: provideBook != null ? provideBook.id : -1,
    title: title,
    coverPath: dbCoverPath,
    filePath: dbFilePath,
    lastReadPosition: '',
    readingPercentage: 0,
    author: author,
    isDeleted: false,
    rating: 0.0,
    createTime: DateTime.now(),
    updateTime: DateTime.now(),
  );

  book.id = await insertBook(book);
  AnxToast.show('Import successful');
  headlessInAppWebView?.dispose();
  headlessInAppWebView = null;
  return;
}

Future<void> getBookMetadata(
  File file, {
  Book? book,
  WidgetRef? ref,
}) async {
  // Validate WebView support before creating headless WebView
  if (!CrossPlatformValidator.isWebViewSupported()) {
    AnxLog.severe('WebView not supported - cannot extract book metadata');
    AnxToast.show('WebView not supported on this platform');
    return;
  }

  String serverFileName = Server().setTempFile(file);

  String cfi = '';

  String bookUrl = 'http://127.0.0.1:${Server().port}/$serverFileName';
  AnxLog.info('import start: book url: $bookUrl');

  HeadlessInAppWebView webview = HeadlessInAppWebView(
    webViewEnvironment: webViewEnvironment,
    initialUrlRequest: URLRequest(
      url: WebUri(generateUrl(
        bookUrl,
        cfi,
        importing: true,
      )),
    ),
    onLoadStop: (controller, url) async {},
    onConsoleMessage: (controller, consoleMessage) {
      if (consoleMessage.message.contains('loadBook')) {
        controller.addJavaScriptHandler(
          handlerName: 'onMetadata',
          callback: (List<dynamic> args) async {
            final metadata = args[0] as Map<String, dynamic>;
            final title = metadata['title'] as String? ?? 'Unknown';
            final dynamic authorData = metadata['author'];
            final String author;

            if (authorData is String) {
              author = authorData;
            } else if (authorData is List) {
              author = authorData
                  .map((dynamic authorItem) {
                    if (authorItem is String) {
                      return authorItem;
                    } else if (authorItem is Map<String, dynamic>) {
                      return authorItem['name'] as String? ?? '';
                    }
                    return '';
                  })
                  .where((String name) => name.isNotEmpty)
                  .join(', ');
            } else {
              author = 'Unknown';
            }

            // base64 cover
            final cover = metadata['cover'] as String? ?? '';
            final description = metadata['description'] as String? ?? '';
            saveBook(file, title, author, description, cover);
            ref?.read(bookListProvider.notifier).refresh();
            // return;
          },
        );
        webviewInitialVariable(controller, bookUrl, cfi, importing: true);
      }
      if (consoleMessage.messageLevel == ConsoleMessageLevel.ERROR) {
        headlessInAppWebView?.dispose();
        headlessInAppWebView = null;
        // throw Exception('Webview: ${consoleMessage.message}');
      }
      webviewConsoleMessage(controller, consoleMessage);
    },
  );

  await webview.dispose();
  await webview.run();
  headlessInAppWebView = webview;

  // max 30s timeout with better logging
  int count = 0;
  while (count < 300) {
    if (headlessInAppWebView == null) {
      AnxLog.info('Metadata extraction completed successfully');
      return;
    }
    await Future<void>.delayed(const Duration(milliseconds: 100));
    count++;

    // Log progress every 5 seconds
    if (count % 50 == 0) {
      AnxLog.info('Metadata extraction progress: ${count / 10}s / 30s');
    }
  }

  headlessInAppWebView?.dispose();
  headlessInAppWebView = null;
  throw Exception('Import: Get book metadata timeout');
}
