import 'dart:math' as math;
import 'package:flutter/material.dart' hide NavigationDestination;
import 'package:dasso_reader/config/navigation_system.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';
import 'package:dasso_reader/config/app_icons.dart';
import 'package:flutter/material.dart' as material;

/// Enhanced Navigation Widgets for Dasso Reader
///
/// This file provides enhanced navigation components that use the NavigationSystem
/// for consistent, accessible, and platform-adaptive navigation.

// =====================================================
// ENHANCED TAB BAR
// =====================================================

/// Enhanced TabBar with improved visual feedback and accessibility
class EnhancedTabBar extends StatelessWidget implements PreferredSizeWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final bool isScrollable;
  final Color? backgroundColor;
  final Color? indicatorColor;

  const EnhancedTabBar({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    this.isScrollable = false,
    this.backgroundColor,
    this.indicatorColor,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: colorScheme.outlineVariant.withAlpha(128),
            width: DesignSystem.spaceMicro,
          ),
        ),
      ),
      child: TabBar(
        isScrollable: isScrollable,
        tabs: destinations.asMap().entries.map((entry) {
          final index = entry.key;
          final destination = entry.value;
          final isSelected = index == currentIndex;

          return NavigationSystem.createNavigationTab(
            context: context,
            destination: destination,
            isSelected: isSelected,
            onTap: () => onDestinationSelected(index),
          );
        }).toList(),
        labelColor: indicatorColor ?? colorScheme.primary,
        unselectedLabelColor: colorScheme.onSurfaceVariant,
        indicatorColor: indicatorColor ?? colorScheme.primary,
        indicatorWeight: DesignSystem.spaceXS - DesignSystem.spaceMicro,
        indicatorSize: TabBarIndicatorSize.tab,
        splashBorderRadius: BorderRadius.circular(DesignSystem.radiusM),
        overlayColor: WidgetStateProperty.resolveWith<Color?>((states) {
          if (states.contains(WidgetState.hovered)) {
            return colorScheme.primary.withAlpha((0.08 * 255).round());
          }
          if (states.contains(WidgetState.pressed)) {
            return colorScheme.primary.withAlpha((0.12 * 255).round());
          }
          return null;
        }),
        onTap: (index) {
          NavigationSystem.provideFeedback(NavigationFeedbackType.selection);
          onDestinationSelected(index);
        },
      ),
    );
  }

  @override
  Size get preferredSize =>
      const Size.fromHeight(DesignSystem.spaceXXL + DesignSystem.spaceL);
}

// =====================================================
// ENHANCED NAVIGATION RAIL
// =====================================================

/// Enhanced NavigationRail with improved visual feedback and responsive behavior
class EnhancedNavigationRail extends StatelessWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final bool extended;
  final Widget? leading;
  final Widget? trailing;
  final Color? backgroundColor;

  // Static const optimization for indicator shape
  static const _indicatorShape = RoundedRectangleBorder(
    borderRadius:
        BorderRadius.all(Radius.circular(16.0)), // DesignSystem.radiusM
  );

  const EnhancedNavigationRail({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    this.extended = false,
    this.leading,
    this.trailing,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    // RepaintBoundary optimization: Isolate NavigationRail rendering
    // This prevents unnecessary repaints when other parts of the UI change
    return RepaintBoundary(
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor ?? colorScheme.surface,
          border: Border(
            right: BorderSide(
              color: colorScheme.outlineVariant.withAlpha(128),
              width: DesignSystem.spaceMicro,
            ),
          ),
        ),
        child: NavigationRail(
          extended: extended,
          selectedIndex: currentIndex,
          onDestinationSelected: (index) {
            NavigationSystem.provideFeedback(NavigationFeedbackType.selection);
            onDestinationSelected(index);
          },
          leading: leading != null ? RepaintBoundary(child: leading!) : null,
          trailing: trailing != null ? RepaintBoundary(child: trailing!) : null,
          destinations: destinations.map((destination) {
            // Optimize with const constructors where possible
            final iconData = destination.icon;
            final selectedIconData = destination.getIcon(selected: true);
            final labelText = destination.getLabel(context);

            return NavigationRailDestination(
              icon: Icon(iconData),
              selectedIcon: Icon(selectedIconData),
              label: Text(labelText),
            );
          }).toList(),
          labelType: extended
              ? NavigationRailLabelType.none
              : NavigationRailLabelType.all,
          backgroundColor: Colors.transparent,
          selectedIconTheme: IconThemeData(
            color: colorScheme.primary,
            size: AppIcons.sizeM,
          ),
          unselectedIconTheme: IconThemeData(
            color: colorScheme.onSurfaceVariant,
            size: AppIcons.sizeM,
          ),
          selectedLabelTextStyle: TextStyle(
            color: colorScheme.primary,
            fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
            fontSize: DesignSystem.fontSizeS,
          ),
          unselectedLabelTextStyle: TextStyle(
            color: colorScheme.onSurfaceVariant,
            fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w500),
            fontSize: DesignSystem.fontSizeS,
          ),
          indicatorColor: colorScheme.primaryContainer,
          indicatorShape: _indicatorShape,
        ),
      ),
    );
  }
}

// =====================================================
// ENHANCED BOTTOM NAVIGATION BAR
// =====================================================

/// Enhanced BottomNavigationBar with improved visual feedback and adaptive height
class EnhancedBottomNavigationBar extends StatelessWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final Color? backgroundColor;
  final double? height;
  final EdgeInsetsGeometry? margin;

  // Static const optimization for indicator shape
  static const _indicatorShape = RoundedRectangleBorder(
    borderRadius:
        BorderRadius.all(Radius.circular(16.0)), // DesignSystem.radiusM
  );

  const EnhancedBottomNavigationBar({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    this.backgroundColor,
    this.height,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    // Calculate adaptive height matching your TabBar implementation
    final adaptiveHeight = height ?? _calculateAdaptiveHeight(context);

    // Use NavigationBar for Material 3 design with enhanced customization
    return Container(
      height: adaptiveHeight,
      margin: margin,
      child: NavigationBar(
        selectedIndex: currentIndex,
        onDestinationSelected: (index) {
          NavigationSystem.provideFeedback(NavigationFeedbackType.selection);
          onDestinationSelected(index);
        },
        backgroundColor: backgroundColor ?? colorScheme.surface,
        indicatorColor: colorScheme.primaryContainer,
        indicatorShape: _indicatorShape,
        destinations: destinations.map((destination) {
          // Optimize with const constructors where possible
          final iconData = destination.icon;
          final selectedIconData = destination.getIcon(selected: true);
          final labelText = destination.getLabel(context);
          final tooltipText = destination.getTooltip(context);

          return material.NavigationDestination(
            icon: Icon(
              iconData,
              size: AppIcons.sizeM, // Consistent with your design system
            ),
            selectedIcon: Icon(
              selectedIconData,
              size: AppIcons.sizeM,
            ),
            label: labelText,
            tooltip: tooltipText,
          );
        }).toList(),
        labelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
        animationDuration: DesignSystem.durationFast,
        // Enhanced elevation for better visual hierarchy with manufacturer adjustments
        elevation: DesignSystem.getAdjustedElevation(DesignSystem.elevationS),
      ),
    );
  }

  /// Calculate adaptive height matching the TabBar implementation
  double _calculateAdaptiveHeight(BuildContext context) {
    final screenSize = ResponsiveSystem.getScreenSize(context);
    final devicePixelRatio =
        screenSize.aspectRatio; // Use aspect ratio as proxy for density
    final textScaler = MediaQuery.textScalerOf(context);

    const baseHeight = DesignSystem.spaceXXL +
        DesignSystem.spaceL +
        DesignSystem.spaceTiny; // Match Material Design standard
    const minHeight = DesignSystem.widgetMinTouchTarget +
        DesignSystem.spaceXS; // Accessibility minimum

    // Scale for device density
    double densityFactor = (devicePixelRatio / 3.0).clamp(0.85, 1.25);

    // Scale for text size
    double textFactor = textScaler.scale(1.0).clamp(0.9, 1.3);

    // Device-specific adjustments
    double deviceFactor = 1.0;
    if (DesignSystem.isSmallPhone(context)) {
      deviceFactor = 0.9;
    } else if (DesignSystem.isTablet(context)) {
      deviceFactor = 1.1;
    }

    double calculatedHeight =
        baseHeight * densityFactor * textFactor * deviceFactor;

    // Ensure accessibility compliance
    return math.max(calculatedHeight, minHeight);
  }
}

// =====================================================
// ADAPTIVE NAVIGATION WRAPPER
// =====================================================

/// Adaptive navigation wrapper that chooses the appropriate navigation pattern
/// based on screen size and platform
class AdaptiveNavigationWrapper extends StatelessWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final Widget child;
  final Widget? leading;
  final Widget? trailing;
  final bool forceBottomNavigation;

  const AdaptiveNavigationWrapper({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    required this.child,
    this.leading,
    this.trailing,
    this.forceBottomNavigation = false,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Show side menu (NavigationRail) only for tablets in landscape orientation
        // or for desktop-sized screens (width-based fallback for desktop support)
        // Mobile devices always use bottom navigation
        final shouldUseRail = !forceBottomNavigation &&
            (constraints.maxWidth > DesignSystem.breakpointDesktop ||
                (DesignSystem.isTablet(context) &&
                    ResponsiveSystem.getOrientation(context) ==
                        Orientation.landscape));

        if (shouldUseRail) {
          // Use NavigationRail for larger screens with RepaintBoundary optimization
          final extended =
              constraints.maxWidth > DesignSystem.breakpointDesktop;

          return Scaffold(
            body: Row(
              children: [
                // RepaintBoundary isolates NavigationRail rendering
                RepaintBoundary(
                  child: EnhancedNavigationRail(
                    destinations: destinations,
                    currentIndex: currentIndex,
                    onDestinationSelected: onDestinationSelected,
                    extended: extended,
                    leading: leading,
                    trailing: trailing,
                  ),
                ),
                // RepaintBoundary isolates main content area rendering
                Expanded(
                  child: RepaintBoundary(child: child),
                ),
              ],
            ),
          );
        } else {
          // Use BottomNavigationBar for smaller screens with RepaintBoundary optimization
          return Scaffold(
            body: RepaintBoundary(child: child),
            bottomNavigationBar: RepaintBoundary(
              child: EnhancedBottomNavigationBar(
                destinations: destinations,
                currentIndex: currentIndex,
                onDestinationSelected: onDestinationSelected,
              ),
            ),
          );
        }
      },
    );
  }
}
