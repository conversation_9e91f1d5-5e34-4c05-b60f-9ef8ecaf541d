import 'package:flutter/material.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';

/// Layout utility widgets for consistent spacing and alignment patterns
/// throughout the Dasso Reader app.
///
/// These utilities help maintain design system consistency and reduce
/// code duplication across components.
class LayoutUtils {
  LayoutUtils._(); // Private constructor to prevent instantiation

  // =====================================================
  // SPACING UTILITIES
  // =====================================================

  /// Creates consistent vertical spacing between form fields
  static Widget formFieldSpacing() => DesignSystem.verticalSpaceM;

  /// Creates consistent vertical spacing between sections
  static Widget sectionSpacing() => DesignSystem.verticalSpaceL;

  /// Creates adaptive vertical spacing based on screen size
  static Widget adaptiveSpacing(BuildContext context) {
    return SizedBox(height: DesignSystem.getAdaptiveSectionSpacing(context));
  }

  /// Creates consistent horizontal spacing between elements
  static Widget horizontalSpacing() => DesignSystem.horizontalSpaceM;

  /// Creates small horizontal spacing for compact layouts
  static Widget horizontalSpacingSmall() => DesignSystem.horizontalSpaceS;

  // =====================================================
  // CONTAINER UTILITIES
  // =====================================================

  /// Creates a standardized card container with consistent padding and styling
  static Widget cardContainer({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    Color? backgroundColor,
    double? elevation,
    BorderRadius? borderRadius,
    BuildContext? context,
  }) {
    return Builder(
      builder: (builderContext) {
        final effectiveContext = context ?? builderContext;
        final colorScheme = Theme.of(effectiveContext).colorScheme;

        return Container(
          padding: padding ?? DesignSystem.cardContentPadding,
          margin: margin ?? DesignSystem.cardMargin,
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius:
                borderRadius ?? BorderRadius.circular(DesignSystem.radiusM),
            boxShadow: elevation != null
                ? [
                    BoxShadow(
                      color: DesignSystem.getStateLayerColor(
                        colorScheme.shadow,
                        0.1,
                      ),
                      blurRadius: elevation,
                      offset: Offset(0, elevation / 2),
                    ),
                  ]
                : null,
          ),
          child: child,
        );
      },
    );
  }

  /// Creates a standardized list item container with consistent padding
  static Widget listItemContainer({
    required Widget child,
    bool isCompact = false,
    VoidCallback? onTap,
    Color? backgroundColor,
  }) {
    final padding = isCompact
        ? DesignSystem.listItemPaddingCompact
        : DesignSystem.listItemPadding;

    Widget container = Container(
      padding: padding,
      color: backgroundColor,
      child: child,
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        child: container,
      );
    }

    return container;
  }

  /// Creates a standardized section header with consistent styling
  static Widget sectionHeader({
    required String title,
    Widget? trailing,
    TextStyle? textStyle,
    EdgeInsetsGeometry? padding,
  }) {
    return Container(
      padding: padding ?? DesignSystem.sectionHeaderPadding,
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: textStyle,
            ),
          ),
          if (trailing != null) trailing,
        ],
      ),
    );
  }

  // =====================================================
  // BUTTON UTILITIES
  // =====================================================

  /// Creates a standardized button with consistent padding and constraints
  static Widget standardButton({
    required Widget child,
    required VoidCallback? onPressed,
    bool isCompact = false,
    ButtonStyle? style,
  }) {
    final padding = isCompact
        ? DesignSystem.compactButtonPadding
        : DesignSystem.standardButtonPadding;

    return ConstrainedBox(
      constraints: DesignSystem.getMinTouchTargetConstraints(),
      child: ElevatedButton(
        onPressed: onPressed,
        style: style?.copyWith(
              padding: WidgetStateProperty.all(padding),
            ) ??
            ElevatedButton.styleFrom(
              padding: padding,
            ),
        child: child,
      ),
    );
  }

  /// Creates a standardized icon button with proper touch targets
  static Widget iconButton({
    required IconData icon,
    required VoidCallback? onPressed,
    String? tooltip,
    Color? color,
    double? size,
  }) {
    return ConstrainedBox(
      constraints: DesignSystem.getMinTouchTargetConstraints(),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, color: color, size: size),
        tooltip: tooltip,
        padding: DesignSystem.iconButtonPadding,
      ),
    );
  }

  // =====================================================
  // LAYOUT PATTERNS
  // =====================================================

  /// Creates a responsive two-column layout that adapts to screen size
  static Widget responsiveColumns({
    required Widget left,
    required Widget right,
    double breakpoint = 600,
    double spacing = 16,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= breakpoint) {
          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(child: left),
              SizedBox(width: spacing),
              Expanded(child: right),
            ],
          );
        } else {
          return Column(
            children: [
              left,
              SizedBox(height: spacing),
              right,
            ],
          );
        }
      },
    );
  }

  /// Creates a centered content container with adaptive max width
  static Widget centeredContent({
    required Widget child,
    double? maxWidth,
    EdgeInsetsGeometry? padding,
  }) {
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: maxWidth ?? 800,
        ),
        child: Padding(
          padding: padding ?? DesignSystem.pagePadding,
          child: child,
        ),
      ),
    );
  }

  /// Creates a standardized form layout with consistent spacing
  static Widget formLayout({
    required List<Widget> children,
    EdgeInsetsGeometry? padding,
    double? spacing,
  }) {
    return Padding(
      padding: padding ?? DesignSystem.pagePadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: _intersperse(
          children,
          SizedBox(height: spacing ?? DesignSystem.formFieldSpacing),
        ),
      ),
    );
  }

  // =====================================================
  // HELPER METHODS
  // =====================================================

  /// Intersperses a separator widget between list items
  static List<Widget> _intersperse(List<Widget> list, Widget separator) {
    if (list.isEmpty) return list;

    final result = <Widget>[];
    for (int i = 0; i < list.length; i++) {
      result.add(list[i]);
      if (i < list.length - 1) {
        result.add(separator);
      }
    }
    return result;
  }

  /// Creates a divider with consistent spacing
  static Widget divider({
    Color? color,
    double? thickness,
    EdgeInsetsGeometry? margin,
  }) {
    return Container(
      height: thickness ?? DesignSystem.widgetDividerHeight,
      margin:
          margin ?? const EdgeInsets.symmetric(vertical: DesignSystem.spaceM),
      color: color,
    );
  }

  /// Creates adaptive grid spacing based on screen size
  static double getAdaptiveGridSpacing(BuildContext context) {
    if (DesignSystem.isDesktop(context)) {
      return DesignSystem.spaceL;
    } else if (DesignSystem.isTablet(context)) {
      return DesignSystem.spaceM;
    } else {
      return DesignSystem.spaceS;
    }
  }

  // =====================================================
  // RESPONSIVE LAYOUT PATTERNS
  // =====================================================

  /// Creates a responsive master-detail layout for tablets and desktop
  static Widget responsiveMasterDetail({
    required BuildContext context,
    required Widget master,
    required Widget detail,
    double masterFlex = 1,
    double detailFlex = 2,
    double breakpoint = 768,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= breakpoint) {
          // Side-by-side layout for larger screens
          return Row(
            children: [
              Expanded(flex: masterFlex.round(), child: master),
              const VerticalDivider(width: 1),
              Expanded(flex: detailFlex.round(), child: detail),
            ],
          );
        } else {
          // Stacked layout for smaller screens
          return detail; // Show detail by default, master accessible via navigation
        }
      },
    );
  }

  /// Creates a responsive navigation layout
  static Widget responsiveNavigation({
    required BuildContext context,
    required Widget body,
    required List<NavigationDestination> destinations,
    int selectedIndex = 0,
    ValueChanged<int>? onDestinationSelected,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Show side menu (NavigationRail) only for tablets in landscape orientation
        // or for desktop-sized screens (width-based fallback for desktop support)
        // Optimized calculation - cache expensive DesignSystem.isTablet() call
        final isTablet = DesignSystem.isTablet(context);
        final shouldUseRail =
            constraints.maxWidth >= DesignSystem.breakpointDesktop ||
                (isTablet && ResponsiveSystem.isLandscape(context));

        if (shouldUseRail) {
          // Use NavigationRail for larger screens with RepaintBoundary optimization
          return Row(
            children: [
              RepaintBoundary(
                child: NavigationRail(
                  destinations: destinations.map(
                    (dest) {
                      // Optimize with const constructors where possible
                      return NavigationRailDestination(
                        icon: dest.icon,
                        selectedIcon: dest.selectedIcon,
                        label: Text(dest.label),
                      );
                    },
                  ).toList(),
                  selectedIndex: selectedIndex,
                  onDestinationSelected: onDestinationSelected,
                  extended:
                      constraints.maxWidth >= DesignSystem.breakpointDesktop,
                ),
              ),
              const VerticalDivider(thickness: 1, width: 1),
              Expanded(child: RepaintBoundary(child: body)),
            ],
          );
        } else {
          // Use BottomNavigationBar for smaller screens with RepaintBoundary optimization
          return Scaffold(
            body: RepaintBoundary(child: body),
            bottomNavigationBar: RepaintBoundary(
              child: NavigationBar(
                destinations: destinations,
                selectedIndex: selectedIndex,
                onDestinationSelected: onDestinationSelected,
              ),
            ),
          );
        }
      },
    );
  }

  /// Creates adaptive dialog sizing
  static Widget adaptiveDialog({
    required BuildContext context,
    required Widget child,
    String? title,
    List<Widget>? actions,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final dialogWidth = DesignSystem.getAdaptiveDialogWidth(context);

        return AlertDialog(
          title: title != null ? Text(title) : null,
          content: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: dialogWidth,
              maxHeight: constraints.maxHeight * 0.8,
            ),
            child: child,
          ),
          actions: actions,
        );
      },
    );
  }

  /// Creates responsive safe area with adaptive padding
  static Widget responsiveSafeArea({
    required Widget child,
    bool top = true,
    bool bottom = true,
    bool left = true,
    bool right = true,
  }) {
    return Builder(
      builder: (context) {
        return SafeArea(
          top: top,
          bottom: bottom,
          left: left,
          right: right,
          child: Padding(
            padding: DesignSystem.getAdaptiveContentPadding(context),
            child: child,
          ),
        );
      },
    );
  }
}

/// Extension methods for common layout patterns
extension LayoutExtensions on Widget {
  /// Wraps the widget with consistent padding
  Widget withPadding([EdgeInsetsGeometry? padding]) {
    return Padding(
      padding: padding ?? DesignSystem.pagePadding,
      child: this,
    );
  }

  /// Wraps the widget with adaptive padding based on screen size
  Widget withAdaptivePadding(BuildContext context) {
    return Padding(
      padding: DesignSystem.getAdaptiveContentPadding(context),
      child: this,
    );
  }

  /// Wraps the widget in a card container
  Widget asCard({
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    Color? backgroundColor,
    double? elevation,
    BuildContext? context,
  }) {
    return LayoutUtils.cardContainer(
      padding: padding,
      margin: margin,
      backgroundColor: backgroundColor,
      elevation: elevation,
      context: context,
      child: this,
    );
  }

  /// Centers the widget with adaptive max width
  Widget centered({double? maxWidth, EdgeInsetsGeometry? padding}) {
    return LayoutUtils.centeredContent(
      maxWidth: maxWidth,
      padding: padding,
      child: this,
    );
  }

  /// Wraps the widget with responsive constraints
  Widget withResponsiveConstraints(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxWidth: DesignSystem.getAdaptiveDialogWidth(context),
      ),
      child: this,
    );
  }

  /// Wraps the widget with touch target constraints for accessibility
  Widget withTouchTarget(BuildContext context) {
    return ConstrainedBox(
      constraints: DesignSystem.getMinTouchTargetConstraints(),
      child: this,
    );
  }

  /// Makes the widget responsive to orientation changes
  Widget responsiveToOrientation({
    Widget Function(BuildContext, Orientation)? builder,
  }) {
    return OrientationBuilder(
      builder: (context, orientation) {
        return builder?.call(context, orientation) ?? this;
      },
    );
  }

  /// Wraps the widget with adaptive safe area
  Widget withAdaptiveSafeArea({
    bool top = true,
    bool bottom = true,
    bool left = true,
    bool right = true,
  }) {
    return Builder(
      builder: (context) {
        return SafeArea(
          top: top,
          bottom: bottom,
          left: left,
          right: right,
          child: Padding(
            padding: DesignSystem.getAdaptiveContentPadding(context),
            child: this,
          ),
        );
      },
    );
  }
}
